{"welcome": "Welcome", "youwillreceiveadigitcodeonwhatsapptoverifynext": "Enter your phone number to receive a 4-digit OTP on WhatsApp.", "forgetPassword": "Forget password", "createNewAccount": "Create an Account", "dontHaveAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "home": "Home", "shop": "Shop", "account": "Account", "updatePassword": "Update password", "websites": "Websites", "newOrder": "New order", "startWithZero": "7xxxxxxxxx", "store": "Store", "myOrders": "My orders", "language": "Language", "currentLanguage": "English", "selectLanguage": "Select Language", "choosePreferedLanguage": "Choose your preferred language", "theme": "Theme", "usernamePlacholder": "Login with email or code", "validationUsername": "Username is required", "passwordPlacholder": "password", "confirmPasswordPlacholder": "Confirm password", "validationPassword": "Password is too short", "validationRequired": "{field} is required", "@validationRequired": {"placeholders": {"field": {"type": "String"}}}, "validationInvalid": "{field} is invalid", "@validationInvalid": {"placeholders": {"field": {"type": "String"}}}, "validationMinChar": "Minimum of {length} characters", "@validationMinChar": {"placeholders": {"length": {"type": "int"}}}, "validationMaxChar": "Maximum of {length} characters", "@validationMaxChar": {"placeholders": {"length": {"type": "int"}}}, "signIn": "Sign in", "register": "Sign Up", "registerHeader": "Create an account", "fullName": "Full name", "phoneValidationDitigs": "Phone number should be 10 digits", "phone": "Phone number", "password": "Password", "passwordIsShort": "Password is too short.", "strongPassword": "Please use a strong password", "skip": "<PERSON><PERSON>", "next": "Next", "city": "City", "email": "Email", "getStarted": "Get Started", "firstSlideTitle": "SHOP FROM \n INTERNATIONAL WEBSITES", "firstSlideDescription": "Order now from thousands of online international shopping websites and receive it in Iraq", "secondSlideTitle": "TRACK & NOTIFY", "secondSlideDescription": "Track your orders easily and get notification when your order arrives", "thirdSlideTitle": "FAST & RELIABLE", "thirdSlideDescription": "Fastest and most secure wav to ship your items with us", "loading": "Loading...", "loadingMore": "Loading more data...", "createRequestDelivery": "Create Delivery Request", "note": "Note", "ticketWithId": "Ticket #{ticketId}", "@ticketWithId": {"placeholders": {"ticketId": {"type": "String"}}}, "reset": "Reset", "cancel": "Cancel", "accept": "Accept", "reject": "Reject", "notNow": "Not Now", "placeOrder": "Place order", "success": "Success", "uploadImage": "Upload image", "profile": "Profile", "search": "Search", "generalErrorMessage": "An error occured, Please try again.", "acceptOrderDialogTitle": "Accept Order", "rejectOrderDialogTitle": "Reject Order", "orderDialogContent": "Are you sure to {actionText} order for the total of {total}?", "@orderDialogContent": {"placeholders": {"actionText": {"type": "String"}, "total": {"type": "String"}}}, "open": "Open", "close": "Close", "closed": "Closed", "save": "Save", "update": "Update", "edit": "Edit", "updatePhoto": "Update Photo", "logout": "Logout", "conteactUs": "Contact us", "aboutUs": "About us", "faq": "FAQ", "support": "Support", "copounsAndGifts": "Coupons & Gifts", "requestDelivery": "Delivery Request", "balance": "Balance", "version": "Version: {version}", "@version": {"placeholders": {"version": {"type": "String"}}}, "link": "Link", "country": "Country", "size": "Size", "image": "Image", "description": "Description", "quantity": "Quantity", "orderDetails": "Order details", "tryAgain": "Try Again", "transactionType": "Transaction Type", "noTotalOrders": "You have no orders", "noOrders": "There is no orders", "date": "Date", "callUs": "Call us", "subject": "Subject", "clearFilters": "Clear Filters", "idLable": "ID", "status": "Status", "brand": "Brand", "itemPrice": "<PERSON><PERSON>", "itemPriceUSD": "Item Price (USD)", "shippingPriceUSD": "Shipping (USD)", "internalPriceUSD": "Internal Shipping (USD)", "commissionUSD": "Commission (USD)", "tax": "Tax", "totalUSD": "Total (USD)", "createdAt": "Created At", "successTicketCreate": "Ticket has been created successfully", "noData": "No data", "waitSupportMessage": "Please, Wait until a support agent replies.", "body": "Body", "editProfile": "Edit Profile", "details": "Details", "transactions": "Transactions", "totalPruchase": "Total Purchase", "totalDelivered": "Total Delivered", "notifications": "Notifications", "price": "Price", "qty": "QTY", "title": "Title", "replies": "Replies", "allOrders": "All orders", "waiting": "Waiting", "delivered": "Delivered", "inTransit": "In Transit", "cancelled": "Cancelled", "completed": "Completed", "deliveryRequestErrorIn24Hours": "You have 2 requests, You cannot request anymore for next 24hours", "createTicket": "Create Ticket", "createTicketErrorIn24Hours": "You cannot create ticket, You already have an open ticket.", "orderProcessingHelper": "Processing order", "logoutConfirmation": "Are you sure you want to log out?", "letsOrder": "Let's order something", "photoPermissionTitle": "Photo Permission", "photoPermissionDescription": "Photo permission should Be granted to use this feature, would you like to go to app settings to give photo permission?", "submit": "Submit", "exchangeRate": "Exchange Rate", "websiteNotfound": "Website not found", "rejectionReasons": "Rejection Reasons", "reason": "Reasons", "viewReason": "View Reason", "rejectReason": "Reject Reason", "back": "Back", "getOTP": "Get OTP", "dontNeedIt": "Don't need it", "highShipping": "High Shipping Price", "highPrice": "High Item Price", "light": "Light", "dark": "Dark", "help": "Help", "wallet": "Wallet", "tierLevel": "Tier Level", "confirmAgain": "Confirm Again", "checkOrder": "Check Order!", "calculating": "Calculating", "processed": "Processed", "hello": "Hello", "discoverOurServices": "Discover our shipping services", "whatAreYouLookingForToday": "What are you looking for today?", "youHaveProcessedOrders": "You have {count} processed orders", "@youHaveProcessedOrders": {"placeholders": {"count": {"type": "int"}}}, "searchProducts": "Search products or websites...", "quickActions": "Quick Actions", "featuredItems": "Hot Deals For You", "viewAll": "View All", "topBrands": "Top Brands", "buy": "Buy", "confirmOrder": "Confirm Order", "product": "Product", "total": "Total", "areYouSureYouWantToPlaceThisOrder": "Are you sure you want to place this order?", "maybeLater": "Maybe Later", "item": "item", "items": "items", "allBrands": "All Brands", "outlets": "Outlets", "selectYourPreferredLanguage": "Select your preferred language", "goldenPrizma": "Golden Prizma", "english": "English", "arabic": "Arabic", "enterYourPhoneNumberToReceiveVerificationCode": "Enter your phone number to receive a verification code on WhatsApp. You'll use this code to reset your password.", "enterYourPhoneNumberToReceiveVerificationCodeIdentity": "Enter your phone number to receive a verification code on WhatsApp. This helps us verify your identity.", "resendCodeIn": "Resend code in", "ok": "OK", "usernameIsRequired": "Username is required", "subjectIsRequired": "Subject is required", "usDollar": "US Dollar", "euro": "Euro", "britishPound": "British Pound", "japaneseYen": "Japanese Yen", "canadianDollar": "Canadian Dollar", "australianDollar": "Australian Dollar", "summerSale": "Summer Sale", "upTo50Off": "Up to 50% Off", "newCollection": "New Collection", "freshArrivals": "Fresh Arrivals", "freeShipping": "Free Shipping", "onOrdersOver100": "On orders over $100", "nike": "Nike", "adidas": "Adidas", "apple": "Apple", "samsung": "Samsung", "sony": "Sony", "hm": "H&M", "amazon": "Amazon", "ebay": "eBay", "alibaba": "Alibaba", "bestBuy": "Best Buy", "etsy": "Etsy", "orderPlacedSuccessfullyQuantity": "Order placed successfully! Quantity: {quantity}", "@orderPlacedSuccessfullyQuantity": {"placeholders": {"quantity": {"type": "int"}}}, "failedToPlaceOrder": "Failed to place order", "anUnexpectedErrorOccurred": "An unexpected error occurred", "anUnexpectedErrorOccurredWhilePlacingOrder": "An unexpected error occurred while placing the order", "emailIsRequired": "Email is required", "emailIsInvalid": "<PERSON><PERSON> is invalid", "maximum500Characters": "Maximum 500 characters", "verification": "Verification", "didntReceiveTheOTP": "Didn't receive the OTP? ", "resend": "Resend", "depositReceived": "Deposit received! Wallet updated.", "depositStatusUpdated": "Deposit status updated.", "youreNotLoggedIn": "You're not logged in", "signInToAccessYourAccount": "Sign in to access your account", "customerIdCopiedToClipboard": "Customer ID copied to clipboard", "manageYourTransactions": "Manage your transactions", "scheduleDeliveryServices": "Schedule delivery services", "editYourPersonalInformation": "Edit your personal information", "getSupportAndAssistance": "Get support and assistance", "changeAppLanguage": "Change app language", "switchBetweenLightAndDarkMode": "Switch between light and dark mode", "signOutOfYourAccount": "Sign out of your account", "anErrorOccurred": "An error occured", "topUp": "Top Up", "history": "History", "failedToLoadTransactions": "Failed to load transactions", "recentTransactions": "Recent Transactions", "seeAll": "See all", "noTransactionsYet": "No transactions yet", "yourTransactionHistoryWillAppearHere": "Your transaction history will appear here", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "credit": "Credit", "debit": "Debit", "topUpWallet": "Top Up Wallet", "selectPaymentMethod": "Select payment method", "fibBank": "FIB Bank", "quickAmounts": "Quick amounts", "customAmount": "Custom amount", "enterAmount": "Enter amount", "exchangeRateUsdToIqd": "Exchange Rate (USD → IQD)", "loadingExchangeRate": "Loading exchange rate...", "unableToLoadExchangeRate": "Unable to load exchange rate. Please try again.", "retry": "Retry", "youWillPayApproximately": "You will pay approximately {amount} IQD", "@youWillPayApproximately": {"placeholders": {"amount": {"type": "String"}}}, "confirmTopUp": "Confirm Top Up {amount}", "selectPaymentMethodToContinue": "Select a payment method to continue", "transactionHistory": "Transaction History", "selectDateRange": "Select date range...", "all": "All", "cashIn": "Cash-In", "cashOut": "Cash-Out", "noCashInTransactionsFound": "No cash-in transactions found", "noCashOutTransactionsFound": "No cash-out transactions found", "noTransactionsFound": "No transactions found", "noTransactionsFoundInSelectedDateRange": "No transactions found in selected date range", "somethingWentWrong": "Something went wrong", "failedToLoadTransactionsTryAgain": "Failed to load transactions. Please try again.", "pasteProductLink": "Paste Product Link", "productUrl": "Product URL", "enterTheProductLinkYouWantToOrder": "Enter the product link you want to order", "loadingSupportedWebsites": "Loading supported websites...", "clear": "Clear", "pasteFromClipboard": "Paste from clipboard", "pleaseEnterAValidUrl": "Please enter a valid URL", "validUrl": "Valid URL", "invalidUrlFormat": "Invalid URL format", "continueText": "Continue", "urlDetectedInClipboard": "URL detected in clipboard", "paste": "PASTE", "urlPastedSuccessfully": "URL pasted successfully", "noUrlFoundInClipboard": "No URL found in clipboard", "failedToAccessClipboard": "Failed to access clipboard", "loadingWebsiteData": "Loading website data...", "failedToLoadBrands": "Failed to load brands", "failedToLoadCategories": "Failed to load categories", "failedToLoadProducts": "Failed to load products", "searchProductsPlaceholder": "Search products...", "categories": "Categories", "promotions": "Promotions", "filter": "Filter", "onSale": "On Sale", "clearAll": "Clear All", "active": "Active", "inactive": "Inactive", "emailRequired": "Email is required", "pleaseEnterValidEmail": "Please enter a valid email address", "oldPassword": "Old Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "passwordChangedSuccessfully": "Password changed successfully", "pleaseEnterCurrentPassword": "Please enter your current password", "pleaseEnterPassword": "Please enter a password", "passwordMustBeAtLeast8Characters": "Password must be at least 8 characters", "newPasswordMustBeDifferent": "New password must be different from old password", "passwordsDoNotMatch": "Passwords do not match", "forgotPasswordMessage": "If you have forgotten your password, please logout and use the 'Forgot Password' feature.", "thisFieldCannotBeChanged": "This field cannot be changed", "deleteAccountConfirmation": "Are you sure you want to permanently delete your account? This action cannot be undone.", "deleteAccount": "Delete Account", "deleteMyAccount": "Delete My Account", "areYouSureDeleteAccount": "Are you sure you want to delete your account?", "confirm": "Confirm", "failedToLoadDeposits": "Failed to load deposits", "pleaseCheckConnectionAndTryAgain": "Please check your connection and try again", "depositDetails": "De<PERSON>sit Details", "transactionId": "Transaction ID", "amount": "Amount", "gateway": "Gateway", "noDescription": "No description", "amountIqd": "Amount (IQD)", "processedAt": "Processed At", "depositCompletedSuccessfully": "Deposit completed successfully! If your wallet balance hasn't updated yet, please refresh or reopen the wallet screen.", "confirmOrders": "Confirm Orders", "allCategories": "All Categories", "addDescription": "Add description (optional)", "addToCart": "Add to Cart", "addedToCart": "Added to cart successfully!", "inStock": "in stock", "outOfStock": "Out of stock", "buyNow": "Buy Now", "orderPlacedSuccessfully": "Order placed successfully! Quantity: {quantity}", "@orderPlacedSuccessfully": {"placeholders": {"quantity": {"type": "String"}}}, "inStockWithCount": "In Stock: {stock}", "@inStockWithCount": {"placeholders": {"stock": {"type": "String"}}}, "totalItems": "Total ({quantity} {itemText})", "@totalItems": {"placeholders": {"quantity": {"type": "String"}, "itemText": {"type": "String"}}}, "viewProduct": "View Product", "stock": "Stock", "changePassword": "Change Password", "camera": "Camera", "gallery": "Gallery", "selectImageSource": "Select Image Source", "takePhoto": "Take Photo", "chooseFromGallery": "Choose from Gallery", "cropImage": "Crop Image", "uploadingImage": "Uploading image...", "imageUploadFailed": "Failed to upload image", "imageUploadSuccess": "Image uploaded successfully", "personalInformation": "Personal Information", "updateProfile": "Update Profile", "updateYourPassword": "Update your password", "permanentlyDeleteYourAccount": "Permanently delete your account", "filters": "Filters", "applyFilters": "Apply Filters", "resetFilters": "Reset Filters", "filterBy": "Filter by", "sortBy": "Sort by", "processedOrders": "Processed Orders", "noProcessedOrders": "No processed orders", "processedOrdersMessage": "You have {count} processed orders that need action.", "@processedOrdersMessage": {"placeholders": {"count": {"type": "int"}}}, "viewOrders": "View Orders"}