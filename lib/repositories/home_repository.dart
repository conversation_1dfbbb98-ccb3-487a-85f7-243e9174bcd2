import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:goldenprizma/domain/home/<USER>/home_data.dart';
import 'package:goldenprizma/helpers/api_urls.dart';
import 'package:goldenprizma/repositories/abstract/repository.dart';

class HomeRepository extends Repository {
  Future<HomeData> getHomeData() async {
    try {
      Response response = await api.get(
        ApiUrls.home,
        options: Options(headers: {'auth': false}),
      );

      debugPrint('Home data response');

      if (response.data['success'] == true) {
        return HomeData.fromJson(response.data);
      } else {
        throw Exception(
            'Failed to fetch home data: ${response.data['message'] ?? 'Unknown error'}');
      }
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Failed to fetch home data: $e');
    }
  }
}
