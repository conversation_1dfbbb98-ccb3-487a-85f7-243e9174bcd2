abstract class Env {
  static EnvEnum appEnv = EnvEnum.production; // local, stage, production

  // Main API URLs
  static String localeApiUrl =
      'https://golden.test'; // http://********:8000 ******** android emulator host to localhost
  static String stageApiUrl = 'https://staging.goldenprizma.com';
  static String productionApiUrl = 'https://goldenprizma.com';

  // Store/Inventory API URLs
  static String localeStoreApiUrl = 'https://inventory.test';
  static String stageStoreApiUrl = 'https://pos-staging.goldenprizma.com/';
  static String productionStoreApiUrl = 'https://inventory.goldenprizma.com/';

  static String apiVersion = 'v1';

  static String get apiUrl {
    return '$url/api/';
  }

  static String get storeApiUrl {
    return '$storeUrl/api/';
  }

  static String get url {
    if (appEnv == EnvEnum.production) {
      return productionApiUrl;
    }

    if (appEnv == EnvEnum.stage) {
      return stageApiUrl;
    }

    return localeApiUrl;
  }

  static String get storeUrl {
    if (appEnv == EnvEnum.production) {
      return productionStoreApiUrl;
    }

    if (appEnv == EnvEnum.stage) {
      return stageStoreApiUrl;
    }

    return localeStoreApiUrl;
  }

  static const notificationDelay = 4;

  static const chatApiAccessKey = 'a07ac1f7345340b582608d4599646b18b2f913e1e35';
}

enum EnvEnum { production, stage, local }
