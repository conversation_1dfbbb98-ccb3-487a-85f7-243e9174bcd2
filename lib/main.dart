import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get_it/get_it.dart';
import 'package:goldenprizma/domain/accounting/bloc/account_bloc.dart';
import 'package:goldenprizma/domain/accounting/bloc/deposit_bloc.dart';
import 'package:goldenprizma/domain/accounting/bloc/pending_deposit_bloc.dart';
import 'package:goldenprizma/domain/accounting/bloc/transaction_bloc.dart';
import 'package:goldenprizma/domain/accounting/bloc/transaction_filter_bloc.dart';
import 'package:goldenprizma/domain/ads/bloc/advertisement_bloc.dart';
import 'package:goldenprizma/domain/ads/bloc/slides_bloc.dart';
import 'package:goldenprizma/domain/auth/bloc/authentication_bloc.dart';
import 'package:goldenprizma/domain/auth/bloc/login_bloc.dart';
import 'package:goldenprizma/domain/auth/bloc/profile_bloc.dart';
import 'package:goldenprizma/domain/chat_subjects/chat_subjects_bloc.dart';
import 'package:goldenprizma/domain/home/<USER>/home_bloc.dart';
import 'package:goldenprizma/domain/notifications/bloc/notification_bloc.dart';
import 'package:goldenprizma/domain/orders/bloc/order_bloc.dart';
import 'package:goldenprizma/domain/store/bloc/home_bloc.dart';
import 'package:goldenprizma/domain/store/bloc/place_store_order_bloc.dart';
import 'package:goldenprizma/domain/store/cubit/store_filter_cubit.dart';
import 'package:goldenprizma/domain/store/repositories/store_home_repository.dart';
import 'package:goldenprizma/domain/websites/bloc/website_bloc.dart';
import 'package:goldenprizma/domain/store/bloc/banners_bloc.dart';
import 'package:goldenprizma/domain/store/bloc/brands_bloc.dart';
import 'package:goldenprizma/domain/store/bloc/categories_bloc.dart';
import 'package:goldenprizma/domain/store/bloc/products_bloc.dart';
import 'package:goldenprizma/presentation/views/application.dart';
import 'package:goldenprizma/presentation/views/screens/chat_subjects.dart';
import 'package:goldenprizma/presentation/views/screens/profile_screen.dart';
import 'package:goldenprizma/presentation/views/screens/wallet_screen.dart';
import 'package:goldenprizma/presentation/widgets/advertisement_list_view.dart';
import 'package:goldenprizma/presentation/widgets/slider_widget.dart';
import 'package:goldenprizma/repositories/accounting_repository.dart';
import 'package:goldenprizma/repositories/advertisemnt_repository.dart';
import 'package:goldenprizma/repositories/auth_repository.dart';
import 'package:goldenprizma/repositories/chat_subjects_repository.dart';
import 'package:goldenprizma/repositories/city_repository.dart';
import 'package:goldenprizma/repositories/contact_repository.dart';
import 'package:goldenprizma/repositories/conversation_repository.dart';
import 'package:goldenprizma/repositories/country_repository.dart';
import 'package:goldenprizma/repositories/exchange_rate_repository.dart';
import 'package:goldenprizma/repositories/filter_brand_repository.dart';
import 'package:goldenprizma/repositories/filter_size_repository.dart';
import 'package:goldenprizma/repositories/filter_website_repository.dart';
import 'package:goldenprizma/repositories/firebase_token_repository.dart';
import 'package:goldenprizma/repositories/home_repository.dart';
import 'package:goldenprizma/repositories/message_repository.dart';
import 'package:goldenprizma/repositories/notification_repository.dart';
import 'package:goldenprizma/repositories/order_repository.dart';
import 'package:goldenprizma/repositories/size_repository.dart';
import 'package:goldenprizma/repositories/slides_repository.dart';
import 'package:goldenprizma/repositories/support_repository.dart';
import 'package:goldenprizma/repositories/token_repository.dart';
import 'package:goldenprizma/repositories/website_repository.dart';
import 'package:goldenprizma/repositories/brands_repository.dart';
import 'package:goldenprizma/repositories/categories_repository.dart';
import 'package:goldenprizma/repositories/products_repository.dart';
import 'package:goldenprizma/repositories/banners_repository.dart';
import 'package:goldenprizma/repositories/store_order_repository.dart';
import 'package:goldenprizma/services/push_notification_service.dart';
import 'package:goldenprizma/support/http_override.dart';
import 'package:shared_preferences/shared_preferences.dart';

final getIt = GetIt.instance;

/// Create a [AndroidNotificationChannel] for heads up notifications
late AndroidNotificationChannel channel;

/// Initialize the [FlutterLocalNotificationsPlugin] package.
late FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin;

void main() async {
  // override global http for failed self certified ssl
  HttpOverrides.global = MyHttpOverrides();

  WidgetsFlutterBinding.ensureInitialized();

  await Firebase.initializeApp();

  // send error if the app is in release mode
  if (kReleaseMode) {
    FlutterError.onError = (FlutterErrorDetails details) {
      // Filter out image loading errors for missing advertisement images
      if (_shouldIgnoreError(details.exception, details.stack)) {
        return; // Don't report this error
      }
      FirebaseCrashlytics.instance.recordFlutterFatalError(details);
    };

    // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework to Crashlytics
    PlatformDispatcher.instance.onError = (error, stack) {
      // Filter out image loading errors for missing advertisement images
      if (_shouldIgnoreError(error, stack)) {
        return true; // Don't report this error
      }
      FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
      return true;
    };
  } else {
    // In debug mode, also filter console errors
    FlutterError.onError = (FlutterErrorDetails details) {
      if (_shouldIgnoreError(details.exception, details.stack)) {
        // Optionally log a filtered message or ignore completely
        debugPrint('Ignored image loading error: ${details.exception}');
        return;
      }
      // Use default error handling for other errors
      FlutterError.presentError(details);
    };
  }

  // register singeltons (dependencies) in our container (getIt)
  getIt.registerSingleton<OrderRepository>(OrderRepository());
  getIt.registerSingleton<SupportRepository>(SupportRepository());
  getIt.registerSingleton<SizeRepository>(SizeRepository());
  getIt.registerSingleton<CountryRepository>(CountryRepository());
  getIt.registerSingleton<CityRepository>(CityRepository());
  getIt.registerSingleton<TokenRepository>(TokenRepository());
  getIt.registerSingleton<AuthRepository>(AuthRepository());
  getIt.registerSingleton<PushNotificationService>(PushNotificationService());
  getIt.registerSingleton<ContactRepository>(ContactRepository());
  getIt.registerSingleton<NotificationRepository>(NotificationRepository());
  getIt.registerSingleton<FirebaseTokenRepository>(FirebaseTokenRepository());
  getIt.registerSingleton<ExchangeRateRepostory>(ExchangeRateRepostory());
  getIt.registerSingleton<FilterBrandRepository>(FilterBrandRepository());
  getIt.registerSingleton<FilterWebsiteRepository>(FilterWebsiteRepository());
  getIt.registerSingleton<FilterSizeRepository>(FilterSizeRepository());
  getIt.registerSingleton<AccountingRepostory>(AccountingRepostory());
  getIt.registerSingleton<ConversationRepostory>(ConversationRepostory());
  getIt.registerSingleton<MessageRepository>(MessageRepository());
  getIt.registerSingleton<ChatSubjectsRepository>(ChatSubjectsRepository());

  // Store repositories
  getIt.registerSingleton<BrandsRepository>(BrandsRepository());
  getIt.registerSingleton<CategoriesRepository>(CategoriesRepository());
  getIt.registerSingleton<ProductsRepository>(ProductsRepository());
  getIt.registerSingleton<BannersRepository>(BannersRepository());
  getIt.registerSingleton<StoreOrderRepository>(StoreOrderRepository());
  getIt.registerSingleton<StoreHomeRepository>(StoreHomeRepository());

  SystemChrome.setPreferredOrientations(
      [DeviceOrientation.portraitUp, DeviceOrientation.portraitDown]);

  SharedPreferences? pref;
  try {
    pref = await SharedPreferences.getInstance();
  } catch (e) {
    pref = null;
  }
  // pref?.clear();

  String localeCode = pref?.getString('locale') ?? 'en';
  String theme = pref?.getString('theme') ?? 'light';
  bool isOnboardingDone = pref?.getBool('is_onboarded') ?? false;

  String? token;
  try {
    token = await getIt.get<TokenRepository>().get();
  } catch (e) {
    token = null;
  }

  channel = const AndroidNotificationChannel(
    'high_importance_channel', // id
    'High Importance Notifications', // name
    description:
        'This channel is used for important notifications.', // description
    importance: Importance.high,
  );

  flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

  /// Create an Android Notification Channel.
  ///
  /// We use this channel in the `AndroidManifest.xml` file to override the
  /// default FCM channel to enable heads up notifications.
  await flutterLocalNotificationsPlugin
      .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin>()
      ?.createNotificationChannel(channel);

  await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
    alert: true, // Required to display a heads up notification
    badge: true,
    sound: true,
  );

  await FirebaseMessaging.instance.requestPermission(
    alert: true,
    announcement: true,
    badge: true,
    carPlay: false,
    criticalAlert: false,
    provisional: false,
    sound: true,
  );
  // Bloc.observer = AppBlocObserver();
  runApp(MultiBlocProvider(
    providers: [
      BlocProvider(
          create: (context) =>
              NotificationBloc(getIt.get<NotificationRepository>())),
      BlocProvider(
          create: (context) =>
              AuthenticationBloc()..add(AuthenticationUserRequested())),
      BlocProvider(create: (context) => LoginBloc()),
      BlocProvider(
        create: (context) => OrderBloc(
          orderRepository: getIt.get<OrderRepository>(),
        ),
      ),
      BlocProvider(
        create: (context) => SlidesBloc(SlidesRepository()),
        child: const SlideWidget(),
      ),
      BlocProvider(
        create: (context) => AdvertisementBloc(AdvertisementsRepository()),
        child: const AdvertisementListView(),
      ),
      BlocProvider(
        create: (context) => AccountBloc(
          accountingRepostory: getIt.get<AccountingRepostory>(),
        ),
      ),
      BlocProvider(
        create: (context) => DepositBloc(),
      ),
      BlocProvider(
        create: (context) => PendingDepositBloc(
          accountingRepository: getIt.get<AccountingRepostory>(),
        ),
      ),
      BlocProvider(
        create: (context) => TransactionFilterBloc(),
        child: const WalletScreen(),
      ),
      BlocProvider<WebsiteBloc>(
        create: (context) => WebsiteBloc(WebsiteRepository()),
      ),
      BlocProvider(
        create: (context) => TransactionBloc(
          accountingRepostory: getIt.get<AccountingRepostory>(),
          transactionFilterBloc: TransactionFilterBloc(),
        ),
        child: const WalletScreen(),
      ),
      BlocProvider(
        create: (context) => ChatSubjectsBloc(
          repository: getIt.get<ChatSubjectsRepository>(),
        ),
        child: const ChatSubjects(),
      ),
      BlocProvider(
        create: (context) => ProfileBloc(),
        child: const ProfileScreen(),
      ),
      BlocProvider(
        create: (context) => HomeBloc(
          homeRepository: HomeRepository(),
        ),
      ),
      // Store BLoCs
      BlocProvider(
        create: (context) => BannersBloc(
          getIt.get<BannersRepository>(),
        ),
      ),
      BlocProvider(
        create: (context) => BrandsBloc(
          getIt.get<BrandsRepository>(),
        ),
      ),
      BlocProvider(
        create: (context) => CategoriesBloc(
          getIt.get<CategoriesRepository>(),
        ),
      ),
      BlocProvider(
        create: (context) => ProductsBloc(
          getIt.get<ProductsRepository>(),
        ),
      ),
      BlocProvider(
        create: (context) => StoreHomeBloc(getIt.get<StoreHomeRepository>()),
      ),
      BlocProvider(
        create: (context) => PlaceStoreOrderBloc(
            storeOrderRepository: getIt.get<StoreOrderRepository>()),
      ),
      // Store Filter Cubit
      BlocProvider(
        create: (context) => StoreFilterCubit(),
      ),
    ],
    child: Application(
      initialLocale: localeCode,
      theme: theme,
      onboarded: isOnboardingDone,
      token: token,
    ),
  ));
}

/// Filter function to determine if an error should be ignored
bool _shouldIgnoreError(dynamic exception, StackTrace? stackTrace) {
  // Convert exception to string for pattern matching
  final errorString = exception.toString();
  final stackString = stackTrace?.toString() ?? '';

  // Ignore all image loading errors for missing local image files
  // This covers advertisements_image, websites_image, and any other similar patterns
  final imageLoadingErrorPattern = RegExp(
    r'(Invalid argument\(s\): )?No host specified in URI file:///\w+_image/',
    caseSensitive: false,
  );

  if (imageLoadingErrorPattern.hasMatch(errorString)) {
    return true;
  }

  // Ignore network image errors from missing image files (any directory ending with _image)
  if (stackString.contains('NetworkImage._loadAsync') &&
      RegExp(r'\w+_image/').hasMatch(errorString)) {
    return true;
  }

  // Ignore image codec errors for missing image files
  if (errorString.contains('resolving an image codec') &&
      RegExp(r'file:///\w+_image/').hasMatch(errorString)) {
    return true;
  }

  // Add more error patterns to ignore as needed
  // if (errorString.contains('another_pattern_to_ignore')) {
  //   return true;
  // }

  return false; // Report all other errors normally
}
