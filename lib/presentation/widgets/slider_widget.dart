import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/ads/bloc/slides_bloc.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/screens/website_launcher.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import 'package:carousel_slider/carousel_slider.dart' as carousel;

class SlideWidget extends StatefulWidget {
  const SlideWidget({super.key});

  @override
  State<SlideWidget> createState() => _SlideWidgetState();
}

class _SlideWidgetState extends State<SlideWidget> {
  int _current = 0;

  final carousel.CarouselSliderController _controller =
      carousel.CarouselSliderController();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SlidesBloc, SlidesState>(
      builder: (context, state) {
        if (state.status == SlidesStatus.initial) {
          return _shimmerLoader();
        }

        return Stack(
          children: [
            _container(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: carousel.CarouselSlider(
                  carouselController: _controller,
                  items: state.slides
                      .map((slide) => Center(
                            child: GestureDetector(
                              onTap: () {
                                Navigator.of(context)
                                    .push(MaterialPageRoute(builder: (context) {
                                  return WebsiteLauncher(
                                    url: slide.url,
                                    websiteName: '',
                                    minimalDesign: true,
                                  );
                                }));
                              },
                              child: Image.network(
                                slide.image,
                                fit: BoxFit.fill,
                                width: 700,
                              ),
                            ),
                          ))
                      .toList(),
                  options: carousel.CarouselOptions(
                      height: 196,
                      autoPlay: true,
                      aspectRatio: 16 / 9,
                      viewportFraction: 1,
                      enableInfiniteScroll: true,
                      onPageChanged: (index, reason) {
                        setState(() {
                          _current = index;
                        });
                      }),
                ),
              ),
            ),
            Positioned(
                right: 12,
                bottom: 16,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: const BoxDecoration(
                    color: Colors.black,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(8),
                      bottomRight: Radius.circular(8),
                    ),
                  ),
                  child: Text(
                    "${_current + 1}/${state.slides.length}",
                    style: const TextStyle(
                      color: Colors.white70,
                    ),
                  ),
                )),
          ],
        );
      },
    );
  }

  Widget _shimmerLoader() {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);
    return _container(
        child: Shimmer.fromColors(
      baseColor: isDarkMode ? AppColors.appbarDarkColor : Colors.grey.shade300,
      highlightColor: isDarkMode ? Colors.black45 : Colors.grey.shade100,
      enabled: true,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    ));
  }

  Widget _container({required Widget child}) {
    return Padding(
      padding: const EdgeInsets.only(
          top: 16.0, right: 12.0, left: 12.0, bottom: 16.0),
      child: SizedBox(
        width: double.infinity,
        height: 96,
        child: child,
      ),
    );
  }
}
