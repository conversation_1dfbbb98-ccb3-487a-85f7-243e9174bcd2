import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/home/<USER>/home_bloc.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/screens/website_launcher.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import 'package:carousel_slider/carousel_slider.dart' as carousel;

class HomeSlideWidget extends StatefulWidget {
  const HomeSlideWidget({super.key});

  @override
  State<HomeSlideWidget> createState() => _HomeSlideWidgetState();
}

class _HomeSlideWidgetState extends State<HomeSlideWidget> {
  int _current = 0;

  final carousel.CarouselSliderController _controller =
      carousel.CarouselSliderController();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HomeBloc, HomeState>(
      builder: (context, state) {
        if (state.status == HomeStatus.loading) {
          return _shimmerLoader();
        } else if (state.status == HomeStatus.success) {
          return _buildSuccessWidget(state);
        } else if (state.status == HomeStatus.failure) {
          return _buildErrorWidget();
        } else {
          return _shimmerLoader();
        }
      },
    );
  }

  Widget _buildSuccessWidget(HomeState state) {
    if (state.homeData.slides.isEmpty) {
      return const SizedBox.shrink();
    }

    return Stack(
      children: [
        _container(
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: carousel.CarouselSlider(
              carouselController: _controller,
              items: state.homeData.slides
                  .map((slide) => Center(
                        child: GestureDetector(
                          onTap: () {
                            if (slide.url != null && slide.url!.isNotEmpty) {
                              Navigator.of(context)
                                  .push(MaterialPageRoute(builder: (context) {
                                return WebsiteLauncher(
                                  url: slide.url!,
                                  websiteName: '',
                                );
                              }));
                            }
                          },
                          child: Image.network(
                            slide.imageUrl,
                            fit: BoxFit.fitHeight,
                            width: 416,
                            height: 97,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: Colors.grey[300],
                                child: const Icon(
                                  Icons.image_not_supported,
                                  size: 50,
                                  color: Colors.grey,
                                ),
                              );
                            },
                            loadingBuilder: (context, child, loadingProgress) {
                              if (loadingProgress == null) return child;
                              return Center(
                                child: CircularProgressIndicator(
                                  value: loadingProgress.expectedTotalBytes !=
                                          null
                                      ? loadingProgress.cumulativeBytesLoaded /
                                          loadingProgress.expectedTotalBytes!
                                      : null,
                                ),
                              );
                            },
                          ),
                        ),
                      ))
                  .toList(),
              options: carousel.CarouselOptions(
                  height: 97,
                  autoPlay: true,
                  viewportFraction: 1,
                  enableInfiniteScroll: true,
                  onPageChanged: (index, reason) {
                    setState(() {
                      _current = index;
                    });
                  }),
            ),
          ),
        ),
        Positioned(
            right: 12,
            bottom: 16,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: const BoxDecoration(
                color: Colors.black,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8),
                  bottomRight: Radius.circular(8),
                ),
              ),
              child: Text(
                "${_current + 1}/${state.homeData.slides.length}",
                style: const TextStyle(
                  color: Colors.white70,
                ),
              ),
            )),
      ],
    );
  }

  Widget _buildErrorWidget() {
    return _container(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: Text(
            'Unable to load slides',
            style: TextStyle(color: Colors.grey),
          ),
        ),
      ),
    );
  }

  Widget _shimmerLoader() {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);
    return _container(
        child: Shimmer.fromColors(
      baseColor: isDarkMode ? AppColors.appbarDarkColor : Colors.grey.shade300,
      highlightColor: isDarkMode ? Colors.black45 : Colors.grey.shade100,
      enabled: true,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    ));
  }

  Widget _container({required Widget child}) {
    return Padding(
      padding: const EdgeInsets.only(
          top: 16.0, right: 12.0, left: 12.0, bottom: 16.0),
      child: SizedBox(
        width: double.infinity,
        height: 96,
        child: child,
      ),
    );
  }
}
