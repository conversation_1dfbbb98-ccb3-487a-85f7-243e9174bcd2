import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/ads/bloc/advertisement_bloc.dart';
import 'package:goldenprizma/domain/ads/models/advertisement.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/screens/website_launcher.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';

class SimpleAdvertisementList extends StatefulWidget {
  final ScrollController? parentScrollController;

  const SimpleAdvertisementList({
    super.key,
    this.parentScrollController,
  });

  @override
  State<SimpleAdvertisementList> createState() =>
      _SimpleAdvertisementListState();
}

class _SimpleAdvertisementListState extends State<SimpleAdvertisementList> {
  @override
  void initState() {
    super.initState();
    // Use parent scroll controller if provided, otherwise widget won't have infinite scroll
    widget.parentScrollController?.addListener(_onScroll);
  }

  @override
  void dispose() {
    widget.parentScrollController?.removeListener(_onScroll);
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) {
      context.read<AdvertisementBloc>().add(AdvertisementRequestLoad());
    }
  }

  bool get _isBottom {
    if (widget.parentScrollController == null ||
        !widget.parentScrollController!.hasClients) {
      return false;
    }
    final maxScroll = widget.parentScrollController!.position.maxScrollExtent;
    final currentScroll = widget.parentScrollController!.offset;
    return currentScroll >= (maxScroll * 0.7);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AdvertisementBloc, AdvertisementState>(
      builder: (context, state) {
        if (state.status == AdvertisementStatus.initial) {
          return _buildShimmerList(context);
        }

        if ((state.status == AdvertisementStatus.success ||
                state.status == AdvertisementStatus.failure) &&
            state.advertismenets.isEmpty) {
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(context.loc.noData),
            ),
          );
        }

        return Padding(
          padding: const EdgeInsets.only(bottom: 64.0),
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemBuilder: (BuildContext context, int index) {
              if (index < state.advertismenets.length) {
                return _buildAdvertisementItem(
                    context, state.advertismenets[index]);
              }

              // Show loading indicator when loading more
              if (state.status == AdvertisementStatus.loading &&
                  !state.hasReachedMax) {
                return const SizedBox(
                  height: 80,
                  child: Padding(
                    padding: EdgeInsets.only(bottom: 16),
                    child: Center(child: CircularProgressIndicator.adaptive()),
                  ),
                );
              }

              return const SizedBox.shrink();
            },
            separatorBuilder: (_, int index) => const SizedBox(height: 8),
            itemCount: state.advertismenets.length +
                (state.status == AdvertisementStatus.loading &&
                        !state.hasReachedMax
                    ? 1
                    : 0),
          ),
        );
      },
    );
  }

  Widget _buildAdvertisementItem(
      BuildContext context, Advertisement advertisement) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(MaterialPageRoute(builder: (context) {
          return WebsiteLauncher(
            url: advertisement.url,
            websiteName: '',
            minimalDesign: true,
          );
        }));
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Image.network(
            advertisement.image,
            fit: BoxFit.cover,
            width: double.infinity,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                height: 180,
                color: Colors.grey[300],
                child: const Center(
                  child: Icon(
                    Icons.error_outline,
                    color: Colors.grey,
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildShimmerList(BuildContext context) {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);

    return Shimmer.fromColors(
      baseColor: isDarkMode ? AppColors.boxDarkColor : Colors.grey.shade300,
      highlightColor: isDarkMode ? Colors.black45 : Colors.grey.shade100,
      enabled: true,
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemBuilder: (_, __) => Container(
          height: 180,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        separatorBuilder: (_, __) => const SizedBox(height: 16),
        itemCount: 2,
      ),
    );
  }
}
