import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/ads/bloc/advertisement_bloc.dart';
import 'package:goldenprizma/domain/ads/models/advertisement.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/screens/website_launcher.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';

class AdvertisementCardGrid extends StatefulWidget {
  const AdvertisementCardGrid({super.key});

  @override
  State<AdvertisementCardGrid> createState() => _AdvertisementCardGridState();
}

class _AdvertisementCardGridState extends State<AdvertisementCardGrid> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    _scrollController.addListener(_onScroll);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AdvertisementBloc, AdvertisementState>(
      builder: (context, state) {
        if (state.status == AdvertisementStatus.initial) {
          return _buildShimmerGrid();
        }

        if ((state.status == AdvertisementStatus.success ||
                state.status == AdvertisementStatus.failure) &&
            state.advertismenets.isEmpty) {
          return Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(context.loc.noData),
            ),
          );
        }

        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.8,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
            ),
            itemCount: state.advertismenets.length > 4
                ? 4
                : state.advertismenets.length,
            itemBuilder: (context, index) {
              return _buildAdvertisementCard(state.advertismenets[index]);
            },
          ),
        );
      },
    );
  }

  Widget _buildAdvertisementCard(Advertisement advertisement) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(MaterialPageRoute(builder: (context) {
          return WebsiteLauncher(
            url: advertisement.url,
            websiteName: '',
            minimalDesign: true,
          );
        }));
      },
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                  ),
                  child: Image.network(
                    advertisement.image,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return const Center(
                        child: Icon(
                          Icons.error_outline,
                          color: Colors.grey,
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildShimmerGrid() {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Shimmer.fromColors(
        baseColor: isDarkMode ? AppColors.boxDarkColor : Colors.grey.shade300,
        highlightColor: isDarkMode ? Colors.black45 : Colors.grey.shade100,
        enabled: true,
        child: GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 0.8,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
          ),
          itemCount: 4,
          itemBuilder: (_, __) => Card(
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _onScroll() {
    if (_isBottom) {
      context.read<AdvertisementBloc>().add(AdvertisementRequestLoad());
    }
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;

    double maxScroll = _scrollController.position.maxScrollExtent;
    double currentScroll = _scrollController.position.pixels;
    double delta = 200.0;
    return maxScroll - currentScroll <= delta &&
        _scrollController.position.pixels != 0;
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }
}
