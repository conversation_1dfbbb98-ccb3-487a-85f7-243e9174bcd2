import 'dart:io';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
// import 'package:goldenprizma/domain/notifications/bloc/notification_bloc.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/main.dart';
import 'package:goldenprizma/presentation/theme/theme_constants.dart';
import 'package:goldenprizma/presentation/views/screens/check_auth_screen.dart';
import 'package:goldenprizma/presentation/views/screens/locale_screen.dart';
import 'package:goldenprizma/repositories/firebase_token_repository.dart';
import 'package:goldenprizma/resources/ku_delegates/ku_cupertino_localizations.dart';
import 'package:goldenprizma/resources/ku_delegates/ku_material_localization_delegate.dart';
import 'package:goldenprizma/resources/ku_delegates/ku_widget_localization_delegate.dart';
import 'package:goldenprizma/support/routes.dart';
import 'package:provider/provider.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

class Application extends StatefulWidget {
  final String initialLocale;
  final String theme;
  final bool onboarded;
  final String? token;

  const Application({
    super.key,
    required this.initialLocale,
    required this.theme,
    required this.onboarded,
    required this.token,
  });

  @override
  State<Application> createState() => _ApplicationState();
}

class _ApplicationState extends State<Application> {
  Future<void> setupInteractedMessage() async {
    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    const initializationSettingsAndroid =
        AndroidInitializationSettings('mipmap/ic_stat_logo_transparent');
    flutterLocalNotificationsPlugin.initialize(
      const InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      ),
      onDidReceiveNotificationResponse: (details) {
        final type = details.payload;
        debugPrint('Notification tapped with type: $type');
        // navigatorKey.currentState?.push(ChatScreen.pageRoute());
        // if (type == 'chat_message') {
        //   Navigator.of(context).push(ChatScreen.pageRoute());
        // } else {
        //   context
        //       .read<NotificationBloc>()
        //       .add(NotificationRequestRefresh());
        // }
      },
    );

    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      RemoteNotification? notification = message.notification;
      debugPrint('Got a message whilst in the foreground! ${message.data}');
      // AndroidNotification? android = message.notification?.android;
      if (Platform.isAndroid && notification != null) {
        flutterLocalNotificationsPlugin.show(
          notification.hashCode,
          notification.title,
          notification.body,
          NotificationDetails(
            android: AndroidNotificationDetails(
              channel.id,
              channel.name,
              channelDescription: channel.description,
            ),
          ),
          payload: message.data['type'] ?? '',
        );
      }
    });

    FirebaseMessaging.instance.onTokenRefresh.listen((token) {
      getIt
          .get<FirebaseTokenRepository>()
          .updateTokenForCurrentDevice(token: token);
    });
  }

  @override
  void initState() {
    super.initState();
    setupInteractedMessage();
  }

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
        providers: [
          ChangeNotifierProvider.value(
            value: AppProvider(widget.initialLocale, widget.theme),
          ),
        ],
        builder: (context, child) {
          final locale = Provider.of<AppProvider>(context).locale;
          final themeMode = Provider.of<AppProvider>(context).currentTheme;

          return MaterialApp(
            title: 'Golden Prizma',
            debugShowCheckedModeBanner: false,
            navigatorKey: navigatorKey,
            themeMode: themeMode,
            theme: GoldenThemes.light,
            darkTheme: GoldenThemes.dark,
            home: Directionality(
              textDirection: locale.languageCode == 'en'
                  ? TextDirection.ltr
                  : TextDirection.rtl,
              // child: BlocProvider(
              //   create: (context) => OrderFilterBloc(),
              //   child: const TestOrderScreen(),
              // ),
              child: !widget.onboarded
                  ? const LocaleScreen()
                  : const CheckAuthScreen(),
            ),
            locale: locale,
            routes: routes(),
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
              KuMaterialLocalizations.delegate,
              KuWidgetLocalizations.delegate,
              KuCupertinoLocalizations.delegate,
            ],
            supportedLocales: AppLocalizations.supportedLocales,
          );
        });
  }
}
