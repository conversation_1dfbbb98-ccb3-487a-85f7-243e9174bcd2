import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/domain/websites/bloc/website_bloc.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/screens/paste_link_browser_screen.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:ionicons/ionicons.dart';
import 'package:provider/provider.dart';
import 'package:toastification/toastification.dart';
import 'package:validators/validators.dart';

/// Screen for entering product URL before scraping
class PasteLinkInputScreen extends StatefulWidget {
  const PasteLinkInputScreen({super.key});

  @override
  State<PasteLinkInputScreen> createState() => _PasteLinkInputScreenState();
}

class _PasteLinkInputScreenState extends State<PasteLinkInputScreen>
    with TickerProviderStateMixin {
  final TextEditingController _urlController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _isValidUrl = false;
  bool _isPasting = false;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _urlController.addListener(_validateUrl);
    _focusNode.addListener(_onFocusChange);

    // Ensure website data is loaded for URL detection
    _loadWebsiteDataIfNeeded();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();

    // Auto-focus and auto-paste if clipboard contains URL
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  void _loadWebsiteDataIfNeeded() {
    final websiteState = context.read<WebsiteBloc>().state;
    if (websiteState.status == WebsiteStatus.initial) {
      context.read<WebsiteBloc>().add(PopularWebsiteFetched());
    }
  }

  void _onFocusChange() {
    setState(() {}); // Rebuild to update UI based on focus state
  }

  void _validateUrl() {
    setState(() {
      _isValidUrl =
          _urlController.text.isNotEmpty && isURL(_urlController.text);
    });
  }

  Future<void> _handlePaste() async {
    setState(() {
      _isPasting = true;
    });

    try {
      ClipboardData? clipboard = await Clipboard.getData(Clipboard.kTextPlain);
      var text = clipboard?.text ?? '';

      // Extract URL from text if it contains other content
      if (!text.startsWith(RegExp(r'(http|https)'))) {
        final httpIndex = text.indexOf('http');
        if (httpIndex != -1) {
          text = text.substring(httpIndex);
          // Find the end of URL (space, newline, etc.)
          final match = RegExp(r'https?://[^\s]+').firstMatch(text);
          if (match != null) {
            text = match.group(0) ?? text;
          }
        }
      }

      if (text.isNotEmpty) {
        _urlController.text = text;
        // Show success feedback
        HapticFeedback.lightImpact();
      } else {
        if (mounted) {
          toastification.show(
            type: ToastificationType.error,
            alignment: Alignment.topCenter,
            context: context,
            title: context.loc.noUrlFoundInClipboard,
            autoCloseDuration: const Duration(seconds: 5),
            showProgressBar: false,
          );
        }
      }
    } catch (e) {
      if (mounted) {
        toastification.show(
          type: ToastificationType.error,
          alignment: Alignment.topCenter,
          context: context,
          title: context.loc.failedToAccessClipboard,
          autoCloseDuration: const Duration(seconds: 5),
          showProgressBar: false,
        );
      }
    }

    setState(() {
      _isPasting = false;
    });
  }

  void _handleNext() {
    if (_isValidUrl) {
      HapticFeedback.selectionClick();

      // Ensure website data is loaded before proceeding
      final websiteState = context.read<WebsiteBloc>().state;
      if (websiteState.status == WebsiteStatus.initial ||
          websiteState.status == WebsiteStatus.refresh) {
        // Show loading indicator and wait for websites to load
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return BlocListener<WebsiteBloc, WebsiteState>(
              listener: (context, state) {
                if (state.status == WebsiteStatus.success ||
                    state.status == WebsiteStatus.failure) {
                  Navigator.of(context).pop(); // Close loading dialog
                  _navigateToBrowserScreen();
                }
              },
              child: AlertDialog(
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const CircularProgressIndicator(
                        color: AppColors.primaryColor),
                    const SizedBox(height: 16),
                    Text(context.loc.loadingWebsiteData),
                  ],
                ),
              ),
            );
          },
        );

        // Trigger website loading if not already done
        if (websiteState.status == WebsiteStatus.initial) {
          context.read<WebsiteBloc>().add(PopularWebsiteFetched());
        }
      } else {
        _navigateToBrowserScreen();
      }
    }
  }

  void _navigateToBrowserScreen() {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            PasteLinkBrowserScreen(url: _urlController.text),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.ease;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);

    return Scaffold(
      backgroundColor: isDarkMode ? AppColors.darkBodyColor : Colors.white,
      appBar: AppBar(
        title: Text(context.loc.pasteProductLink),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Ionicons.close),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: BlocBuilder<WebsiteBloc, WebsiteState>(
        builder: (context, websiteState) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 8),

                  // URL input section with better spacing and visual hierarchy
                  Text(
                    context.loc.productUrl,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    context.loc.enterTheProductLinkYouWantToOrder,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: Colors.grey.shade600,
                        ),
                  ),

                  const SizedBox(height: 24),

                  // Show website loading status if needed
                  if (websiteState.status == WebsiteStatus.refresh)
                    Container(
                      padding: const EdgeInsets.all(12),
                      margin: const EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        color: AppColors.primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: AppColors.primaryColor,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Text(context.loc.loadingSupportedWebsites),
                        ],
                      ),
                    ),

                  // Enhanced URL input field with better UX
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: _focusNode.hasFocus
                          ? [
                              BoxShadow(
                                color: AppColors.primaryColor.withOpacity(0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ]
                          : [],
                    ),
                    child: TextField(
                      controller: _urlController,
                      focusNode: _focusNode,
                      keyboardType: TextInputType.url,
                      textInputAction: TextInputAction.done,
                      onSubmitted: (_) => _handleNext(),
                      style: Theme.of(context).textTheme.bodyLarge,
                      decoration: InputDecoration(
                        hintText: 'https://example.com/product',
                        hintStyle: TextStyle(color: Colors.grey.shade400),
                        prefixIcon: AnimatedContainer(
                          duration: const Duration(milliseconds: 200),
                          child: Icon(
                            HugeIcons.strokeRoundedLink06,
                            color: _focusNode.hasFocus
                                ? AppColors.primaryColor
                                : Colors.grey.shade400,
                          ),
                        ),
                        suffixIcon: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (_urlController.text.isNotEmpty)
                              IconButton(
                                onPressed: () {
                                  _urlController.clear();
                                  HapticFeedback.lightImpact();
                                },
                                icon: Icon(Icons.clear,
                                    color: Colors.grey.shade400),
                                tooltip: context.loc.clear,
                                splashRadius: 20,
                              ),
                            IconButton(
                              onPressed: _isPasting ? null : _handlePaste,
                              icon: _isPasting
                                  ? const SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        color: AppColors.primaryColor,
                                      ),
                                    )
                                  : const Icon(
                                      Icons.paste,
                                      color: AppColors.primaryColor,
                                    ),
                              tooltip: context.loc.pasteFromClipboard,
                              splashRadius: 20,
                            ),
                          ],
                        ),
                        filled: true,
                        fillColor: Colors.grey.shade50,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(16),
                          borderSide: BorderSide(color: Colors.grey.shade200),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(16),
                          borderSide: BorderSide(color: Colors.grey.shade200),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(16),
                          borderSide: const BorderSide(
                            color: AppColors.primaryColor,
                            width: 2,
                          ),
                        ),
                        errorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(16),
                          borderSide:
                              const BorderSide(color: Colors.red, width: 2),
                        ),
                        focusedErrorBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(16),
                          borderSide:
                              const BorderSide(color: Colors.red, width: 2),
                        ),
                        errorText:
                            _urlController.text.isNotEmpty && !_isValidUrl
                                ? context.loc.pleaseEnterAValidUrl
                                : null,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 16,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // URL validation feedback
                  if (_urlController.text.isNotEmpty)
                    AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      child: Row(
                        children: [
                          Icon(
                            _isValidUrl ? Icons.check_circle : Icons.error,
                            color: _isValidUrl ? Colors.green : Colors.red,
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            _isValidUrl
                                ? context.loc.validUrl
                                : context.loc.invalidUrlFormat,
                            style: Theme.of(context)
                                .textTheme
                                .bodySmall
                                ?.copyWith(
                                  color:
                                      _isValidUrl ? Colors.green : Colors.red,
                                  fontWeight: FontWeight.w500,
                                ),
                          ),
                        ],
                      ),
                    ),

                  const Spacer(),

                  AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    width: double.infinity,
                    height: 56,
                    child: ElevatedButton.icon(
                      onPressed: _isValidUrl ? _handleNext : null,
                      icon: const Icon(HugeIcons.strokeRoundedArrowRight01),
                      label: Text(
                        context.loc.continueText,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _isValidUrl
                            ? AppColors.primaryColor
                            : Colors.grey.shade300,
                        foregroundColor:
                            _isValidUrl ? Colors.white : Colors.grey,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        elevation: _isValidUrl ? 2 : 0,
                        shadowColor: AppColors.primaryColor.withOpacity(0.3),
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _urlController.dispose();
    _focusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }
}
