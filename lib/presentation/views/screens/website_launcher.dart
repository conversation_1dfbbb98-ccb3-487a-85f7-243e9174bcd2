// ignore_for_file: use_build_context_synchronously
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/domain/websites/bloc/website_bloc.dart';
import 'package:goldenprizma/domain/websites/models/website.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/helpers/web_scraper.dart';
import 'package:goldenprizma/presentation/components/webview_navigation_controls.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/screens/new_order_screen.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:webview_flutter/webview_flutter.dart';
// Import for Android features.
// import 'package:webview_flutter_android/webview_flutter_android.dart';
// ignore: depend_on_referenced_packages
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';

class WebsiteLauncher extends StatefulWidget {
  final String websiteName;
  final bool minimalDesign;

  WebsiteLauncher({
    super.key,
    required this.url,
    required this.websiteName,
    this.minimalDesign = false,
  });

  final String url;
  final WebViewController controller = WebViewController();

  @override
  State<WebsiteLauncher> createState() => _WebsiteLauncherState();
}

class _WebsiteLauncherState extends State<WebsiteLauncher> {
  bool _isLoading = false;
  Offset? _buttonPosition;
  late final WebViewController _controller;
  late final Website? website;
  File? image;
  double? price;
  String? itemName;
  bool _cookieSet = false;
  bool processing = false;

  Future<void> enableCookies() async {
    try {
      WebViewCookieManager cookieManager = WebViewCookieManager();
      await cookieManager.setCookie(
        const WebViewCookie(
          name: "currency",
          value: "AED",
          domain: ".shein.com",
          path: "/",
        ),
      );
    } catch (e) {
      debugPrint('Error: $e');
    }
  }

  @override
  void initState() {
    super.initState();

    if (mounted) {
      website = context.read<WebsiteBloc>().state.findWebsiteByUrl(widget.url);
      // debugPrint('Website: $website');
    }

    late final PlatformWebViewControllerCreationParams params;

    if (WebViewPlatform.instance is WebKitWebViewPlatform) {
      params = WebKitWebViewControllerCreationParams(
        allowsInlineMediaPlayback: true,
        mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
      );
    } else {
      params = const PlatformWebViewControllerCreationParams();
    }

    final WebViewController controller =
        WebViewController.fromPlatformCreationParams(params);

    controller
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(onProgress: (int progress) {
          debugPrint('WebView is loading (progress : $progress%)');
        }, onPageStarted: (String url) {
          if (mounted) {
            setState(() {
              _isLoading = true;
            });
          }
        }, onPageFinished: (String url) async {
          if (mounted) {
            setState(() {
              _isLoading = false;
            });
          }

          try {
            Uri uri = Uri.parse(url);
            final bool isShein = uri.host.contains('m.shein.com') ||
                uri.host.contains('ar.shein.com');

            // Set the cookie only once
            if (!_cookieSet && isShein) {
              debugPrint('Setting cookie for Shein');
              await enableCookies();
              await controller.runJavaScript(
                "document.cookie = 'currency=AED; domain=.shein.com; path=/;';",
              );
              _cookieSet = true; // Mark the cookie as set
              await controller.reload(); // Reload the page to apply the cookie
            }
          } catch (e) {
            debugPrint('Error: $e');
          }
        }, onWebResourceError: (WebResourceError error) {
          debugPrint('''
              Page resource error:
              code: ${error.errorCode}
              description: ${error.description}
              errorType: ${error.errorType}
              isForMainFrame: ${error.isForMainFrame}
          ''');
        }, onNavigationRequest: (NavigationRequest request) {
          return NavigationDecision.navigate;
        }, onHttpError: (HttpResponseError error) {
          debugPrint('Error occurred on page: ${error.response?.statusCode}');
        }, onUrlChange: (UrlChange change) async {
          if (_isLoading) {
            return;
          }
        }),
      )
      ..addJavaScriptChannel(
        'Toaster',
        onMessageReceived: (JavaScriptMessage message) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(message.message)),
          );
        },
      )
      ..loadRequest(Uri.parse(widget.url));

    _controller = controller;
  }

  Future<File?> scrapImage(WebViewController controller) async {
    return await WebScraper().getImage(
      script: website?.imageScript ?? 'return "";',
      controller: controller,
    );
  }

  Future<double> scrapPrice(WebViewController controller,
      [bool converToAED = false]) async {
    final lPrice = await WebScraper().getPrice(
      script: website?.priceScript ?? 'return 0;',
      controller: controller,
      convertToAed: converToAED,
    );

    debugPrint("price $lPrice");

    return lPrice ?? 0.0;
  }

  Future<String?> scrapName(WebViewController controller) async {
    final name = await WebScraper().getItemName(
      script: website?.nameScript ?? 'return "";',
      controller: controller,
    );
    debugPrint("name is = $name");
    return name;
  }

  Future<void> scrapData(WebViewController controller) async {
    await scrapImage(controller);
    await scrapPrice(controller);
    await scrapName(controller);
  }

  @override
  Widget build(BuildContext context) {
    if (_buttonPosition == null) {
      final dimension = MediaQuery.of(context).size;
      debugPrint("height: ${dimension.height}");
      _buttonPosition = Offset((dimension.width / 2) - 96,
          dimension.height - (dimension.height * 0.3));
    }

    return Scaffold(
      appBar: AppBar(
        centerTitle: false,
        titleSpacing: 0,
        elevation: 0,
        leadingWidth: 40,
        backgroundColor: Colors.transparent,
        // backgroundColor: Theme.of(context).colorScheme.surface,
        leading: IconButton(
          padding: const EdgeInsets.all(0),
          visualDensity: const VisualDensity(horizontal: 0, vertical: -4),
          splashRadius: 20,
          tooltip: "Close",
          onPressed: () {
            Navigator.pop(context);
          },
          icon: const Icon(HugeIcons.strokeRoundedCancel01),
        ),
        actions: [
          if (!widget.minimalDesign)
            NavigationControls(controller: _controller),
          if (!widget.minimalDesign)
            IconButton(
                padding: const EdgeInsets.all(0),
                visualDensity:
                    const VisualDensity(horizontal: -4, vertical: -4),
                splashRadius: 20,
                onPressed: () async {
                  await Clipboard.setData(ClipboardData(
                      text: await _controller.currentUrl() ?? widget.url));
                  ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
                    content: Text('Link copied!'),
                    behavior: SnackBarBehavior.floating,
                  ));
                },
                icon: const Icon(
                  HugeIcons.strokeRoundedCopy01,
                  size: 20,
                )),
          if (!widget.minimalDesign)
            IconButton(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                visualDensity:
                    const VisualDensity(horizontal: -1, vertical: -1),
                splashRadius: 20,
                onPressed: () {
                  Share.share(widget.url);
                },
                icon: const Icon(
                  HugeIcons.strokeRoundedShare08,
                  size: 20,
                )),
        ],
        title: widget.minimalDesign
            ? const SizedBox()
            : SizedBox(
                height: 24,
                child: FutureBuilder(
                    future: Future.value(_controller.currentUrl()),
                    builder: (context, snapshot) {
                      if (snapshot.hasData) {
                        return Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 2.5),
                          decoration: BoxDecoration(
                            color: Provider.of<AppProvider>(context)
                                    .isDarkMode(context)
                                ? AppColors.boxDarkColor
                                : Colors.white,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            snapshot.data.toString(),
                            style: Theme.of(context).textTheme.labelLarge,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        );
                      }
                      return const SizedBox();
                    }),
              ),
      ),
      body: Stack(
        children: [
          WebViewWidget(
            controller: _controller,
          ),
          if (_isLoading)
            Center(
              child: Container(
                padding: const EdgeInsetsDirectional.all(16.0),
                decoration: BoxDecoration(
                    color: Provider.of<AppProvider>(context).isDarkMode(context)
                        ? AppColors.boxDarkColor
                        : Colors.white,
                    borderRadius: BorderRadius.circular(15),
                    boxShadow: const <BoxShadow>[
                      BoxShadow(
                          color: Colors.grey,
                          offset: Offset.zero,
                          blurRadius: 3,
                          spreadRadius: 0.1)
                    ]),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const CircularProgressIndicator(strokeWidth: 8),
                    const SizedBox(height: 12),
                    Text(
                      context.loc.loading,
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          Positioned(
            left: _buttonPosition?.dx ?? 96,
            top: _buttonPosition?.dy ?? 100,
            child: GestureDetector(
              onPanUpdate: (details) {
                if (mounted) {
                  setState(() {
                    _buttonPosition = _buttonPosition != null
                        ? _buttonPosition! + details.delta
                        : null;
                  });
                }
              },
              child: widget.minimalDesign ? null : _floatingActionButton(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _floatingActionButton() {
    return FittedBox(
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24),
        child: Container(
          decoration: BoxDecoration(
            shape: BoxShape.rectangle,
            color: Theme.of(context).colorScheme.primary,
          ),
          child: Row(
            children: [
              Container(
                width: 56,
                height: 48,
                decoration: const BoxDecoration(
                  color: AppColors.primaryBrightColor,
                ),
                child: const Icon(
                  HugeIcons.strokeRoundedDrag01,
                  color: Colors.white,
                ),
              ),
              MaterialButton(
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
                elevation: 0,
                textColor: Colors.white,
                onPressed: () async {
                  String? link = await _controller.currentUrl();
                  if (processing) {
                    return;
                  }

                  if (link != null) {
                    setState(() {
                      processing = true;
                    });
                    try {
                      Uri uri = Uri.parse(link);
                      final bool isShein = uri.host.contains('shein.com');

                      Navigator.push(
                          context,
                          NewOrderScreen.pageRoute(
                            link: link,
                            website: website,
                            image: await scrapImage(_controller),
                            price: await scrapPrice(_controller, isShein),
                            itemName: await scrapName(_controller),
                          ));
                    } catch (e) {
                      Navigator.push(
                          context,
                          NewOrderScreen.pageRoute(
                            link: link,
                            website: website,
                          ));
                    } finally {
                      setState(() {
                        processing = false;
                      });
                    }
                  }
                },
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(0),
                ),
                child: Row(
                  children: [
                    if (!processing)
                      const Icon(
                        size: 22,
                        HugeIcons.strokeRoundedShoppingBagAdd,
                        color: Colors.white,
                      ),
                    if (!processing) const SizedBox(width: 4),
                    processing
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation(Colors.white),
                            ),
                          )
                        : Text(
                            context.loc.placeOrder,
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(color: Colors.white),
                          ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
