import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/domain/websites/bloc/website_bloc.dart';
import 'package:goldenprizma/domain/websites/models/website.dart';
import 'package:goldenprizma/helpers/web_scraper.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/screens/new_order_screen.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:provider/provider.dart';
import 'package:webview_flutter/webview_flutter.dart';

/// Browser screen that scrapes product data automatically
class PasteLinkBrowserScreen extends StatefulWidget {
  final String url;

  const PasteLinkBrowserScreen({
    super.key,
    required this.url,
  });

  @override
  State<PasteLinkBrowserScreen> createState() => _PasteLinkBrowserScreenState();
}

class _PasteLinkBrowserScreenState extends State<PasteLinkBrowserScreen>
    with TickerProviderStateMixin {
  late WebViewController _controller;
  late AnimationController _loadingAnimationController;
  late AnimationController _successAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  Website? _website;
  bool _isPageLoaded = false;
  bool _isScrapingComplete = false;
  String _currentStatus = 'Loading page...';
  File? _scrapedImage;
  double? _scrapedPrice;
  String? _scrapedItemName;

  // Timeout mechanism
  Timer? _timeoutTimer;
  bool _hasTimedOut = false;
  static const Duration _timeoutDuration =
      Duration(seconds: 12); // 12 second timeout

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeWebView(); // Initialize WebView synchronously for build method
    _initializeAsync(); // Call async initialization separately
  }

  Future<void> _initializeAsync() async {
    await _findWebsite();

    // Only start loading if website is supported
    if (_website != null) {
      _controller.loadRequest(Uri.parse(widget.url));
      _startTimeoutTimer();
    }
  }

  void _startTimeoutTimer() {
    _timeoutTimer = Timer(_timeoutDuration, () {
      if (!_isScrapingComplete && mounted) {
        setState(() {
          _hasTimedOut = true;
          _currentStatus = 'Loading is taking longer than expected...';
        });
      }
    });
  }

  void _cancelTimeoutTimer() {
    _timeoutTimer?.cancel();
    _timeoutTimer = null;
  }

  void _skipLoading() {
    _cancelTimeoutTimer();

    if (mounted) {
      setState(() {
        _currentStatus = 'Skipping to order creation...';
      });
    }

    // Brief delay to show the status change
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _navigateToOrderScreen();
      }
    });
  }

  void _initializeAnimations() {
    _loadingAnimationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _successAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _successAnimationController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _successAnimationController,
      curve: Curves.elasticOut,
    ));
  }

  Future<void> _findWebsite() async {
    try {
      _website = context.read<WebsiteBloc>().state.findWebsiteByUrl(widget.url);
      debugPrint('Found website: ${_website?.name}');
    } catch (e) {
      _website = null;
    }

    if (_website == null) {
      if (mounted) {
        setState(() {
          _currentStatus = 'Scarping not supported ';
        });
      }
      await Future.delayed(const Duration(milliseconds: 800));
      _navigateToOrderScreen();
      return;
    }
  }

  void _initializeWebView() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setUserAgent(
          'Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15')
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            if (mounted) {
              setState(() {
                _currentStatus = 'Loading page...';
              });
            }
          },
          onPageFinished: (String url) async {
            if (mounted) {
              setState(() {
                _isPageLoaded = true;
                _currentStatus = 'Extracting product information...';
              });
            }

            // Wait for dynamic content with intelligent detection
            await _waitForDynamicContent();
            _startScraping();
          },
          onWebResourceError: (WebResourceError error) {
            _handleError('Failed to load page');
          },
        ),
      );

    // URL loading is now handled in _initializeAsync after website check
  }

  Future<void> _waitForDynamicContent() async {
    if (mounted) {
      setState(() {
        _currentStatus = 'Waiting for page content to load...';
      });
    }

    // Try to detect if content is ready by checking for key elements
    const maxWaitTime = Duration(seconds: 3);
    const checkInterval = Duration(milliseconds: 300);
    final startTime = DateTime.now();

    while (DateTime.now().difference(startTime) < maxWaitTime) {
      try {
        // Check if we can find any of the expected elements
        bool hasContent = false;

        // Check for image element if we have an image script
        if (_website?.imageScript?.isNotEmpty == true) {
          final imageResult = await _controller.runJavaScriptReturningResult(
              'document.querySelector("img") !== null');
          if (imageResult == true) hasContent = true;
        }

        // Check for price element if we have a price script
        if (_website?.priceScript?.isNotEmpty == true && !hasContent) {
          final priceResult = await _controller.runJavaScriptReturningResult(
              'document.body.innerText.length > 100');
          if (priceResult == true) hasContent = true;
        }

        // If we found content or this is a simple site, we're ready
        if (hasContent) {
          if (mounted) {
            setState(() {
              _currentStatus = 'Content loaded - starting extraction...';
            });
          }
          await Future.delayed(const Duration(milliseconds: 500));
          return;
        }

        // Wait before next check
        await Future.delayed(checkInterval);
      } catch (e) {
        // If JavaScript fails, just continue - page might be ready anyway
        break;
      }
    }

    // Fallback: if we couldn't detect content, wait a shorter time
    if (mounted) {
      setState(() {
        _currentStatus = 'Proceeding with extraction...';
      });
    }
    await Future.delayed(const Duration(milliseconds: 800));
  }

  Future<void> _startScraping() async {
    if (_website == null) {
      _handleError('Website not supported');
      return;
    }

    try {
      final webScraper = WebScraper();

      // Scrape name
      if (_website?.nameScript?.isNotEmpty == true) {
        if (mounted) {
          setState(() {
            _currentStatus = 'Getting product name...';
          });
        }
        _scrapedItemName = await webScraper.getItemName(
          script: "${_website?.nameScript}",
          controller: _controller,
        );
        await Future.delayed(const Duration(milliseconds: 500));
      }

      // Scrape price
      if (_website?.priceScript?.isNotEmpty == true) {
        if (mounted) {
          setState(() {
            _currentStatus = 'Getting product price...';
          });
        }
        final uri = Uri.parse(widget.url);
        final isShein = uri.host.contains('shein.com');
        _scrapedPrice = await webScraper.getPrice(
          script: "${_website?.priceScript}",
          controller: _controller,
          convertToAed: isShein,
        );
        await Future.delayed(const Duration(milliseconds: 500));
      }

      // Scrape image
      if (_website?.imageScript?.isNotEmpty == true) {
        if (mounted) {
          setState(() {
            _currentStatus = 'Getting product image...';
          });
        }
        _scrapedImage = await webScraper.getImage(
          script: "${_website?.imageScript}",
          controller: _controller,
        );
        await Future.delayed(const Duration(milliseconds: 500));
      }

      if (mounted) {
        setState(() {
          _currentStatus = 'Complete! Preparing your order...';
          _isScrapingComplete = true;
        });
      }

      _cancelTimeoutTimer(); // Cancel timeout since we completed successfully
      _successAnimationController.forward();

      // Wait for success animation to complete
      await Future.delayed(const Duration(milliseconds: 1200));
      if (mounted) {
        _navigateToOrderScreen();
      }
    } catch (e) {
      _handleError('Failed to extract product information');
    }
  }

  void _handleError(String message) {
    _cancelTimeoutTimer(); // Cancel timeout since we're handling an error

    if (mounted) {
      setState(() {
        _currentStatus = message;
      });
    }

    // Show error for a moment then still navigate to order screen
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        _navigateToOrderScreen();
      }
    });
  }

  void _navigateToOrderScreen() {
    Navigator.pushReplacement(
      context,
      NewOrderScreen.pageRoute(
        link: widget.url,
        website: _website,
        image: _scrapedImage,
        price: _scrapedPrice,
        itemName: _scrapedItemName,
        fromBrowserScreen: true,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);

    return Scaffold(
      backgroundColor: isDarkMode ? AppColors.darkBodyColor : Colors.white,
      body: SafeArea(
        child: Stack(
          children: [
            // Hidden WebView
            Positioned(
              left: -1000,
              top: -1000,
              child: SizedBox(
                width: 1,
                height: 1,
                child: WebViewWidget(controller: _controller),
              ),
            ),

            // Loading UI
            Center(
              child: Padding(
                padding: const EdgeInsets.all(32.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Loading animation
                    _buildLoadingAnimation(),

                    const SizedBox(height: 48),

                    // Status text
                    Text(
                      _currentStatus,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 16),

                    // Progress indicator
                    _buildProgressIndicator(),

                    const SizedBox(height: 32),

                    // URL display
                    _buildUrlDisplay(),

                    // Skip button (only show after timeout)
                    if (_hasTimedOut && !_isScrapingComplete) ...[
                      const SizedBox(height: 32),
                      _buildSkipButton(),
                    ],
                  ],
                ),
              ),
            ),

            // Close button
            Positioned(
              top: 16,
              right: 16,
              child: IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.close),
                style: IconButton.styleFrom(
                  backgroundColor: Colors.black.withOpacity(0.1),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingAnimation() {
    if (_isScrapingComplete) {
      return AnimatedBuilder(
        animation: _successAnimationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Opacity(
              opacity: _fadeAnimation.value,
              child: Container(
                width: 100,
                height: 100,
                decoration: const BoxDecoration(
                  color: Colors.green,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 50,
                ),
              ),
            ),
          );
        },
      );
    }

    return AnimatedBuilder(
      animation: _loadingAnimationController,
      builder: (context, child) {
        return Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: SweepGradient(
              colors: [
                AppColors.primaryColor.withOpacity(0.1),
                AppColors.primaryColor,
                AppColors.primaryColor.withOpacity(0.1),
              ],
              stops: const [0.0, 0.5, 1.0],
              transform: GradientRotation(
                  _loadingAnimationController.value * 2 * 3.14159),
            ),
          ),
          child: Center(
            child: Container(
              width: 70,
              height: 70,
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                HugeIcons.strokeRoundedShoppingBag01,
                color: AppColors.primaryColor,
                size: 30,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildProgressIndicator() {
    double progress = 0.0;

    if (_isPageLoaded) progress = 0.3;
    if (_scrapedItemName != null) progress = 0.5;
    if (_scrapedPrice != null) progress = 0.7;
    if (_scrapedImage != null) progress = 0.9;
    if (_isScrapingComplete) progress = 1.0;

    return Column(
      children: [
        Container(
          width: double.infinity,
          height: 4,
          decoration: BoxDecoration(
            color: Colors.grey.shade300,
            borderRadius: BorderRadius.circular(2),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: progress,
            child: Container(
              decoration: BoxDecoration(
                color: AppColors.primaryColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '${(progress * 100).toInt()}% Complete',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey.shade600,
              ),
        ),
      ],
    );
  }

  Widget _buildUrlDisplay() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            HugeIcons.strokeRoundedLink06,
            color: Colors.grey.shade600,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              widget.url,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey.shade600,
                  ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSkipButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: _skipLoading,
        icon: const Icon(
          HugeIcons.strokeRoundedArrowRight02,
          size: 18,
        ),
        label: const Text(
          'Skip and Continue',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _loadingAnimationController.dispose();
    _successAnimationController.dispose();
    _cancelTimeoutTimer();
    super.dispose();
  }
}
