// ignore_for_file: use_build_context_synchronously

import 'dart:async';
import 'dart:io';

import 'package:dotted_border/dotted_border.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:formz/formz.dart';
import 'package:goldenprizma/domain/exchange_rates/bloc/exchange_rate_bloc.dart';
import 'package:goldenprizma/domain/orders/bloc/order_form_bloc.dart';
import 'package:goldenprizma/domain/orders/models/country_model.dart';
import 'package:goldenprizma/domain/orders/models/size_model.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/domain/websites/models/website.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/helpers/custom_dialogs.dart';
import 'package:goldenprizma/main.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/forms/order/country_input.dart';
import 'package:goldenprizma/presentation/views/forms/order/currency_input.dart';
import 'package:goldenprizma/presentation/views/forms/order/description_input.dart';
import 'package:goldenprizma/presentation/views/forms/order/image_input.dart';
import 'package:goldenprizma/presentation/views/forms/order/item_price_input.dart';
import 'package:goldenprizma/presentation/views/forms/order/link_input.dart';
import 'package:goldenprizma/presentation/views/forms/order/order_form_model.dart';
import 'package:goldenprizma/presentation/views/forms/order/quantity_field.dart';
import 'package:goldenprizma/presentation/views/forms/order/size_input.dart';
import 'package:goldenprizma/presentation/views/screens/home_screen.dart';
import 'package:goldenprizma/presentation/widgets/auth_wrapper.dart';
import 'package:goldenprizma/repositories/country_repository.dart';
import 'package:goldenprizma/repositories/order_repository.dart';
import 'package:goldenprizma/repositories/size_repository.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ionicons/ionicons.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';

class NewOrderScreen extends StatefulWidget {
  const NewOrderScreen({
    super.key,
    this.link,
    this.website,
    this.image,
    this.price,
    this.itemName,
  });

  final String? link;
  final Website? website;
  final File? image;
  final double? price;
  final String? itemName;

  static const routeName = '/orders/new';

  static PageRouteBuilder pageRoute({
    String? link,
    Website? website,
    File? image,
    double? price,
    String? itemName,
  }) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) =>
          MultiBlocProvider(
        providers: [
          BlocProvider<OrderFormBloc>(
              create: (context) =>
                  OrderFormBloc(orderRepository: getIt.get<OrderRepository>())),
          BlocProvider<ExchangeRateBloc>(
              create: (context) => ExchangeRateBloc()),
        ],
        child: AuthWrapper(
            child: NewOrderScreen(
          link: link,
          website: website,
          image: image,
          price: price,
          itemName: itemName,
        )),
      ),
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(0.0, 1.0);
        const end = Offset.zero;

        var curve = Curves.ease;

        var tween =
            Tween(begin: begin, end: end).chain(CurveTween(curve: curve));

        final offsetAnimation = animation.drive(tween);
        return SlideTransition(
          position: offsetAnimation,
          child: child,
        );
      },
    );
  }

  @override
  _NewOrderScreenState createState() => _NewOrderScreenState();
}

class _NewOrderScreenState extends State<NewOrderScreen> {
  final TextEditingController _linkInputController = TextEditingController();
  final TextEditingController _sizeEditTextController = TextEditingController();
  final TextEditingController _descriptionInputController =
      TextEditingController();
  final TextEditingController _quantityController =
      TextEditingController(text: '1');
  final SizeRepository _sizeRepository = getIt.get<SizeRepository>();
  final CountryRepository _countryRepository = getIt.get<CountryRepository>();
  final ImagePicker _picker = ImagePicker();
  File? imageFile;
  bool _showLoadingDialog = false;
  final List<CountryModel> _countries = [];

  @override
  void initState() {
    super.initState();

    _loadCountries();

    if (widget.image != null) {
      imageFile = widget.image;
    }

    if (widget.link != null && widget.link!.isNotEmpty) {
      _linkInputController.value = TextEditingValue(text: widget.link ?? '');

      context.read<OrderFormBloc>().add(
            OrderFormChanged(
              form: OrderFormModel(
                linkInput: LinkInput.dirty(widget.link ?? ''),
                imageInput: widget.image != null
                    ? ImageInput.dirty(widget.image!)
                    : const ImageInput.pure(),
                itemPriceInput: widget.price != null
                    ? ItemPriceInput.dirty(widget.price)
                    : const ItemPriceInput.pure(),
                countryInput: CountryInput.dirty(widget.website?.countryId),
              ),
            ),
          );
      context.read<OrderFormBloc>().add(ScrapLink(link: "${widget.link}"));
    }
  }

  @override
  Widget build(BuildContext context) {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);
    return Scaffold(
      appBar: _appBar(context),
      body: SingleChildScrollView(
        child: BlocListener<OrderFormBloc, OrderFormState>(
          listener: (context, state) {
            if (state.scrapStatus == ScrapStatus.loading) {
              showDialog(
                  context: context,
                  builder: (context) {
                    return const Center(child: CircularProgressIndicator());
                  });

              setState(() {
                _showLoadingDialog = true;
              });
            }

            if ((state.scrapStatus == ScrapStatus.failure ||
                    state.scrapStatus == ScrapStatus.success) &&
                _showLoadingDialog) {
              Navigator.of(context, rootNavigator: true).pop();
              setState(() {
                _showLoadingDialog = false;
              });
            }

            if (state.scrapStatus == ScrapStatus.success) {
              setState(() {
                imageFile = state.form.imageInput.value;
              });
            }

            if (state.status == FormzStatus.submissionFailure &&
                state.hasServerError &&
                state.apirErrorMessage.isNotEmpty) {
              ScaffoldMessenger.of(context)
                ..hideCurrentSnackBar()
                ..showSnackBar(SnackBar(
                  content: Text(
                    state.apirErrorMessage,
                    style: const TextStyle(color: Colors.white),
                  ),
                  backgroundColor: Colors.red,
                ));
            }

            if (state.status == FormzStatus.submissionSuccess) {
              ScaffoldMessenger.of(context).hideCurrentSnackBar();
              showSuccessDialogWithAutoHide(
                context: context,
                message: state.apiSuccessMessage,
              ).then((value) {
                Navigator.of(context).push(PageRouteBuilder(
                  pageBuilder: (context, animation, secondaryAnimation) =>
                      const HomeScreen(initialPage: 3), // MyOrdersPage
                ));
              });
              _resetFields();
            }
          },
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(context.loc.link,
                    style: Theme.of(context).textTheme.bodySmall),
                const SizedBox(height: 4),
                _buildLinkInput(widget.link, isDarkMode),
                const SizedBox(height: 16),
                Text(context.loc.country,
                    style: Theme.of(context).textTheme.bodySmall),
                const SizedBox(height: 4),
                _buildCountryInput(isDarkMode),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(context.loc.size,
                                  style: Theme.of(context).textTheme.bodySmall),
                              const SizedBox(height: 4),
                              _buildSizeInput(isDarkMode),
                            ],
                          ),
                          const SizedBox(height: 16),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                context.loc.quantity,
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                              const SizedBox(height: 4),
                              _buildQuantityInput(isDarkMode),
                            ],
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(context.loc.image,
                              style: Theme.of(context).textTheme.bodySmall),
                          const SizedBox(height: 4),
                          _imageField(),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(context.loc.description,
                    style: Theme.of(context).textTheme.bodySmall),
                const SizedBox(height: 4),
                _buildDescriptionInput(isDarkMode),
                const SizedBox(height: 6),
                _exchangeRate(),
                const SizedBox(height: 32),
                _submitButton(),
                const SizedBox(height: 16),
                _resetButton(),
              ],
            ),
          ),
        ),
      ),
      backgroundColor: isDarkMode ? AppColors.darkBodyColor : Colors.white,
    );
  }

  Widget _buildLinkInput(String? link, bool isDarkMode) {
    return BlocBuilder<OrderFormBloc, OrderFormState>(
      builder: (context, state) {
        return TextField(
          controller: _linkInputController,
          keyboardType: TextInputType.url,
          decoration: InputDecoration(
            hintText: 'https://example.com',
            errorText: state.form.linkInput.validationErrorMessage(context),
            suffixIcon: IconButton(
              onPressed: () async {
                ClipboardData? clipboard =
                    await Clipboard.getData(Clipboard.kTextPlain);
                var text = clipboard?.text ?? '';
                if (!text.startsWith(RegExp(r'(http|https)'))) {
                  text = text.substring(text.indexOf('http'));
                }
                _linkInputController.text = text;
                context.read<OrderFormBloc>().add(OrderFormChanged(
                      form: state.form.copyWith(
                        linkInput: LinkInput.dirty(_linkInputController.text),
                      ),
                    ));
                if (_linkInputController.text.isNotEmpty) {
                  context
                      .read<OrderFormBloc>()
                      .add(ScrapLink(link: _linkInputController.text));
                }
              },
              icon: const Icon(Icons.paste),
            ),
          ),
          onChanged: (String value) {
            if (!value.startsWith(RegExp(r'(http|https)'))) {
              value = value.substring(value.indexOf('http'));
            }
            _linkInputController.text = value;
            EasyDebounce.debounce('link', const Duration(milliseconds: 600),
                () {
              context.read<OrderFormBloc>().add(OrderFormChanged(
                    form:
                        state.form.copyWith(linkInput: LinkInput.dirty(value)),
                  ));
              if (value.isNotEmpty) {
                context.read<OrderFormBloc>().add(ScrapLink(link: value));
              }
            });
          },
        );
      },
    );
  }

  Widget _buildCountryInput(bool isDarkMode) {
    return BlocBuilder<OrderFormBloc, OrderFormState>(
      builder: (context, state) {
        final selectedCountryId =
            state.form.countryInput.value ?? widget.website?.countryId;

        // select country model from int id
        final CountryModel selectedCountry = _countries.firstWhere(
          (CountryModel country) => country.id == selectedCountryId,
          orElse: () => const CountryModel(),
        );

        context
            .read<ExchangeRateBloc>()
            .add(ExchangeRateRequestLoad(currency: selectedCountry.currency));

        return DropdownSearch<CountryModel>(
          key: UniqueKey(),
          popupProps: const PopupProps.menu(
            showSelectedItems: true,
            isFilterOnline: false,
          ),
          filterFn: (CountryModel? countryModel, filter) {
            return countryModel != null && countryModel.filterByName(filter);
          },
          items: _countries,
          onChanged: (CountryModel? data) {
            context.read<OrderFormBloc>().add(
                  OrderFormChanged(
                    form: state.form.copyWith(
                      countryInput: CountryInput.dirty(data?.id),
                      currencyInput: data?.currency != null
                          ? CurrencyInput.dirty(data?.currency)
                          : const CurrencyInput.pure(),
                    ),
                  ),
                );
            context
                .read<ExchangeRateBloc>()
                .add(ExchangeRateRequestLoad(currency: data?.currency));
          },
          selectedItem: selectedCountry,
          compareFn: (item, selectedItem) => item.id == selectedItem.id,
          dropdownDecoratorProps: DropDownDecoratorProps(
            dropdownSearchDecoration: InputDecoration(
                isCollapsed: true,
                contentPadding:
                    const EdgeInsetsDirectional.fromSTEB(16, 14, 1, 0),
                errorText:
                    state.form.countryInput.validationErrorMessage(context)),
          ),
        );
      },
    );
  }

  Widget _buildQuantityInput(bool isDarkMode) {
    return BlocBuilder<OrderFormBloc, OrderFormState>(
      builder: (context, state) {
        return TextField(
          controller: _quantityController,
          keyboardType: TextInputType.number,
          textAlign: TextAlign.center,
          decoration: InputDecoration(
            errorText: state.form.quantityInput.validationErrorMessage,
            prefixIcon: IconButton(
              onPressed: () {
                int number = int.parse(_quantityController.text);
                number--;
                if (number <= 1) {
                  number = 1;
                }
                _quantityController.text = "$number";
                context.read<OrderFormBloc>().add(
                      OrderFormChanged(
                        form: state.form.copyWith(
                            quantityInput: QuantityInput.dirty("$number")),
                      ),
                    );
              },
              icon: const Icon(Ionicons.remove),
            ),
            suffixIcon: IconButton(
              onPressed: () {
                int number = int.parse(_quantityController.text);
                number++;
                _quantityController.text = "$number";
                context.read<OrderFormBloc>().add(
                      OrderFormChanged(
                        form: state.form.copyWith(
                            quantityInput: QuantityInput.dirty("$number")),
                      ),
                    );
              },
              icon: const Icon(Ionicons.add),
            ),
          ),
          onChanged: (value) {
            context.read<OrderFormBloc>().add(
                  OrderFormChanged(
                    form: state.form.copyWith(
                      quantityInput: QuantityInput.dirty(value),
                    ),
                  ),
                );
          },
        );
      },
    );
  }

  Widget _buildSizeInput(bool isDarkMode) {
    return BlocBuilder<OrderFormBloc, OrderFormState>(
      builder: (context, state) {
        final SizeModel? selectedSize = state.form.sizeInput.value;
        return DropdownSearch<SizeModel>(
            popupProps: PopupProps.modalBottomSheet(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.75,
                minHeight: MediaQuery.of(context).size.height * 0.5,
              ),
              fit: FlexFit.tight,
              scrollbarProps: const ScrollbarProps(thickness: 4),
              showSearchBox: true,
              showSelectedItems: true,
              isFilterOnline: true,
              searchFieldProps: TextFieldProps(
                controller: _sizeEditTextController,
                autofocus: true,
              ),
              modalBottomSheetProps: ModalBottomSheetProps(
                backgroundColor: Theme.of(context).colorScheme.surface,
                animation: AnimationController(
                  vsync: Navigator.of(context),
                  duration: const Duration(milliseconds: 400),
                  reverseDuration: const Duration(milliseconds: 350),
                ),
                shape: ShapeBorder.lerp(
                    RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16)),
                    RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    1),
              ),
              itemBuilder: _customPopupItemBuilder,
            ),
            autoValidateMode: AutovalidateMode.always,
            asyncItems: (String? filter) async {
              List<SizeModel> sizes =
                  await _sizeRepository.getAll(filter: "$filter");

              return sizes;
            },
            onChanged: (SizeModel? data) {
              context.read<OrderFormBloc>().add(
                    OrderFormChanged(
                      form: state.form.copyWith(
                        sizeInput: SizeInput.dirty(data),
                      ),
                    ),
                  );
            },
            selectedItem: selectedSize,
            compareFn: (item, selectedItem) => item.id == selectedItem.id,
            dropdownDecoratorProps: DropDownDecoratorProps(
              dropdownSearchDecoration: InputDecoration(
                errorText: state.form.sizeInput.validationErrorMessage(context),
                isCollapsed: true,
                contentPadding:
                    const EdgeInsetsDirectional.fromSTEB(16, 14, 1, 0),
              ),
            ));
      },
    );
  }

  Widget _buildDescriptionInput(bool isDarkMode) {
    return BlocBuilder<OrderFormBloc, OrderFormState>(
      builder: (context, state) {
        return TextFormField(
          controller: _descriptionInputController,
          keyboardType: TextInputType.multiline,
          maxLength: 500,
          maxLines: 3,
          onChanged: (String value) {
            context.read<OrderFormBloc>().add(
                  OrderFormChanged(
                    form: state.form.copyWith(
                      descriptionInput: DescriptionInput.dirty(value),
                    ),
                  ),
                );
          },
          decoration: InputDecoration(
            errorText: state.form.descriptionInput.validationErrorMessage,
          ),
        );
      },
    );
  }

  Widget _imageField() {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);
    return BlocBuilder<OrderFormBloc, OrderFormState>(
      builder: (context, state) {
        return GestureDetector(
          onTap: () async {
            bool photoPermissionIsDenied = await Permission.photos.isDenied;
            if (Platform.isIOS && !photoPermissionIsDenied) {
              await showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: Text(context.loc.photoPermissionTitle),
                  content: Text(context.loc.photoPermissionDescription),
                  actions: [
                    MaterialButton(
                      onPressed: () async {
                        await openAppSettings();
                      },
                      child: const Text('ok'),
                    )
                  ],
                ),
              );
              return;
            }
            final XFile? image =
                await _picker.pickImage(source: ImageSource.gallery);
            if (image != null) {
              setState(() {
                imageFile = File(image.path);
              });
              context.read<OrderFormBloc>().add(
                    OrderFormChanged(
                      form: state.form.copyWith(
                        imageInput: ImageInput.dirty(File(image.path)),
                      ),
                    ),
                  );
            }
          },
          child: Column(
            children: [
              DottedBorder(
                color: state.form.imageInput.invalid
                    ? Colors.red
                    : isDarkMode
                        ? AppColors.boxDarkColor
                        : Colors.grey.shade200,
                strokeWidth: 2,
                dashPattern: const [8, 4],
                borderType: BorderType.RRect,
                radius: const Radius.circular(10),
                padding: const EdgeInsets.all(6),
                child: ClipRRect(
                  borderRadius: const BorderRadius.all(Radius.circular(12)),
                  child: Column(
                    children: [
                      SizedBox(
                        width: double.infinity,
                        height: 120,
                        child: _imagePlaceholder(),
                      ),
                      if (state.form.imageInput.invalid)
                        Text(
                          state.form.imageInput
                                  .validationErrorMessage(context) ??
                              '',
                          style: const TextStyle(color: Colors.red),
                        )
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _imagePlaceholder() {
    if (imageFile == null) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Ionicons.cloud_upload),
          const SizedBox(height: 8),
          Text(
            context.loc.uploadImage,
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ],
      );
    } else {
      return Image.file(imageFile as File);
    }
  }

  Widget _customPopupItemBuilder(
      BuildContext context, SizeModel? item, bool isSelected) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8),
      decoration: !isSelected
          ? null
          : BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(5),
            ),
      child: ListTile(
        selected: isSelected,
        title: Text(item?.name ?? ''),
      ),
    );
  }

  Future<void> _loadCountries() async {
    List<CountryModel> countries = await _countryRepository.getAll();
    _countries.addAll(countries);
  }

  Widget _exchangeRate() {
    return BlocBuilder<ExchangeRateBloc, ExchangeRateState>(
        builder: (context, state) {
      if (state.status == ExchangeRateStatus.initial ||
          state.status == ExchangeRateStatus.failure) {
        return const SizedBox(width: 0, height: 0);
      }

      if (state.status == ExchangeRateStatus.loading) {
        return const SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator.adaptive(),
        );
      }

      return Row(
        children: [
          Text("${context.loc.exchangeRate}:",
              style: Theme.of(context).textTheme.bodySmall),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.all(3),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: Provider.of<AppProvider>(context).isDarkMode(context)
                  ? AppColors.darkBodyColor
                  : Colors.grey.shade100,
            ),
            child: Text(
              state.exchangeRate.formattedRate,
              style: const TextStyle(fontSize: 12),
            ),
          ),
        ],
      );
    });
  }

  Widget _submitButton() {
    return BlocBuilder<OrderFormBloc, OrderFormState>(
      builder: (context, state) {
        final selectedCountryId =
            state.form.countryInput.value ?? widget.website?.countryId;

        // select country model from int id
        final CountryModel selectedCountry = _countries.firstWhere(
          (CountryModel country) => country.id == selectedCountryId,
          orElse: () => const CountryModel(),
        );

        debugPrint("price is: ${widget.price}");
        return MaterialButton(
          onPressed: state.status.isSubmissionInProgress
              ? null
              : () {
                  context.read<OrderFormBloc>()
                    ..add(OrderFormChanged(
                      form: state.form.copyWith(
                        currencyInput:
                            CurrencyInput.dirty(selectedCountry.currency),
                      ),
                    ))
                    ..add(ClientScraped(
                      websiteId: widget.website?.id,
                      itemPrice: widget.price,
                      itemCode: widget.itemName,
                    ))
                    ..add(const OrderFormSubmitted());
                },
          color: AppColors.primaryColor,
          minWidth: double.infinity,
          height: 50,
          elevation: 0,
          child: state.status.isSubmissionInProgress
              ? const CircularProgressIndicator.adaptive()
              : Text(
                  context.loc.placeOrder,
                  style: const TextStyle(color: Colors.white),
                ),
        );
      },
    );
  }

  Widget _resetButton() {
    return BlocBuilder<OrderFormBloc, OrderFormState>(
      builder: (context, state) {
        return Center(
          child: SizedBox(
            height: 50,
            child: TextButton(
              style: ButtonStyle(
                foregroundColor:
                    WidgetStateProperty.all(AppColors.primaryColor),
                overlayColor: WidgetStateProperty.all(Colors.transparent),
              ),
              onPressed: state.status.isSubmissionInProgress
                  ? null
                  : () {
                      _resetFields();
                      Navigator.of(context).pop();
                    },
              child: Text(
                context.loc.cancel,
              ),
            ),
          ),
        );
      },
    );
  }

  void _resetFields() {
    _sizeEditTextController.text = '';
    _linkInputController.text = '';
    imageFile = null;
    _descriptionInputController.text = '';
    context.read<OrderFormBloc>().add(const OrderFormReset());
  }

  AppBar _appBar(BuildContext context) {
    return AppBar(
      title: Text(AppLocalizations.of(context)!.placeOrder),
      elevation: 0,
      actions: [
        IconButton(
            onPressed: () {
              Navigator.of(context).pushAndRemoveUntil(
                  MaterialPageRoute(builder: (context) {
                return const HomeScreen(
                  initialPage: 2, // Websites Page
                );
              }), (route) => false);
            },
            icon: const Icon(HugeIcons.strokeRoundedGlobe02)),
      ],
      leading: IconButton(
        icon: const Icon(Ionicons.close),
        onPressed: () {
          Navigator.of(context).pop();
        },
      ),
    );
  }

  @override
  void dispose() {
    _sizeEditTextController.dispose();
    _linkInputController.dispose();
    _descriptionInputController.dispose();
    super.dispose();
  }
}
