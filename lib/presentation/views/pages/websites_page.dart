import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_sticky_header/flutter_sticky_header.dart';
import 'package:goldenprizma/domain/providers/app_provider.dart';
import 'package:goldenprizma/domain/websites/bloc/website_bloc.dart';
import 'package:goldenprizma/domain/websites/models/website.dart';
import 'package:goldenprizma/extensions/localization_extension.dart';
import 'package:goldenprizma/local_data/countries.dart';
import 'package:goldenprizma/presentation/theme/app_colors.dart';
import 'package:goldenprizma/presentation/views/screens/website_launcher.dart';
import 'package:ionicons/ionicons.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';

class WebsitesPage extends StatefulWidget {
  const WebsitesPage({super.key});

  @override
  _WebsitesPageState createState() => _WebsitesPageState();
}

class _WebsitesPageState extends State<WebsitesPage>
    with AutomaticKeepAliveClientMixin {
  final TextEditingController _searchController = TextEditingController();
  final List<Map> _countries = countries();
  dynamic _currentCountry;

  @override
  void initState() {
    super.initState();
    context.read<WebsiteBloc>().add(PopularWebsiteFetched());
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: BlocBuilder<WebsiteBloc, WebsiteState>(builder: (context, state) {
        if (state.status == WebsiteStatus.initial ||
            state.status == WebsiteStatus.refresh) {
          return CustomScrollView(slivers: [
            _header(),
            const WebsiteLoadingUI(),
          ]);
        }

        if (state.status == WebsiteStatus.failure) {
          return CustomScrollView(
            slivers: [
              _header(),
              SliverToBoxAdapter(
                child: Center(child: Text(context.loc.websiteNotfound)),
              ),
            ],
          );
        }

        return CustomScrollView(
          slivers: [
            _header(),
            ...state.countryWebsites.map((value) => _StickyHeaderGrid(
                  title: value.name,
                  websites: value.websites,
                )),
            const SliverToBoxAdapter(
              child: SizedBox(height: 64 + (64 / 2)),
            ),
          ],
        );
      }),
    );
  }

  Widget _header() {
    final bool isDarkMode =
        Provider.of<AppProvider>(context).isDarkMode(context);

    return SliverPadding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      sliver: SliverToBoxAdapter(
        child: Container(
          height: 52,
          decoration: BoxDecoration(
            color: isDarkMode ? AppColors.appbarDarkColor : Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isDarkMode
                  ? Colors.white.withOpacity(1)
                  : Colors.grey.shade400,
              width: 1,
            ),
          ),
          child: TextField(
            controller: _searchController,
            autofocus: false,
            style: TextStyle(
              color: isDarkMode ? Colors.white : Colors.black87,
              fontSize: 13.5,
              fontFamily: "NotoSans",
            ),
            decoration: InputDecoration(
              fillColor: Colors.transparent,
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              isDense: true,
              suffixIcon: _filterButton(),
              border: InputBorder.none,
              enabledBorder: InputBorder.none,
              focusedBorder: InputBorder.none,
              prefixIcon: Icon(
                Icons.search_rounded,
                color: isDarkMode ? Colors.white54 : Colors.grey.shade500,
                size: 24,
              ),
              hintText: context.loc.search,
              hintStyle: TextStyle(
                color: isDarkMode ? Colors.white38 : Colors.grey.shade500,
                fontFamily: "NotoSans",
              ),
            ),
            onChanged: (value) {
              EasyDebounce.debounce('search', const Duration(milliseconds: 500),
                  () {
                context
                    .read<WebsiteBloc>()
                    .add(WebsiteSearchChanged(search: value));
              });
            },
          ),
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;

  _filterButton() {
    final bool isDarkMode =
        Provider.of<AppProvider>(context).isDarkMode(context);

    return Container(
      margin: const EdgeInsets.all(6),
      decoration: BoxDecoration(
        color: isDarkMode ? AppColors.boxDarkColor : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              isDarkMode ? Colors.white.withOpacity(0.1) : Colors.grey.shade200,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withOpacity(0.1)
                : Colors.grey.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: _currentCountry != null && _currentCountry['name'] != ''
          ? IconButton(
              onPressed: _countryBottomSheet,
              tooltip: "Filter by country",
              padding: const EdgeInsets.all(6),
              iconSize: 24,
              icon: ClipRRect(
                borderRadius: BorderRadius.circular(6),
                child: Image.asset(
                  _currentCountry['flag'],
                  width: 24,
                  height: 18,
                  fit: BoxFit.cover,
                ),
              ),
            )
          : IconButton(
              tooltip: "Filter by country",
              padding: const EdgeInsets.all(6),
              iconSize: 18,
              onPressed: _countryBottomSheet,
              icon: Icon(
                Ionicons.options,
                color: isDarkMode ? Colors.white60 : Colors.grey.shade600,
              ),
            ),
    );
  }

  _countryBottomSheet() {
    return showMaterialModalBottomSheet(
      expand: false,
      context: context,
      backgroundColor: Colors.transparent,
      bounce: true,
      enableDrag: true,
      builder: (_) {
        return Material(
          clipBehavior: Clip.antiAlias,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
          child: SafeArea(
            top: false,
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: _countries
                    .map((country) => ListTile(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          selected: _currentCountry != null &&
                              _currentCountry['country_id'] ==
                                  country['country_id'],
                          title: Text(country['short_name']),
                          selectedTileColor:
                              Theme.of(context).primaryColor.withOpacity(0.2),
                          leading: country['name'] != ''
                              ? Image.asset(
                                  country['flag'],
                                  width: 32,
                                )
                              : null,
                          onTap: () {
                            setState(() {
                              _currentCountry = country;
                            });
                            Navigator.of(context, rootNavigator: true).pop();
                            context.read<WebsiteBloc>()
                              ..add(
                                WebsiteCountryChanged(
                                  country: {
                                    'country_id': country['country_id'],
                                    'short_name': country['short_name']
                                  },
                                ),
                              )
                              ..add(PopularWebsiteRefreshed());
                          },
                        ))
                    .toList(),
              ),
            ),
          ),
        );
      },
    );
  }
}

class _StickyHeaderGrid extends StatelessWidget {
  const _StickyHeaderGrid({required this.title, required this.websites});

  final String title;
  final List<Website> websites;

  @override
  Widget build(BuildContext context) {
    return SliverStickyHeader(
      overlapsContent: false,
      sticky: true,
      header: Container(
        margin: const EdgeInsets.only(top: 8),
        child: _SideHeader(title: title),
      ),
      sliver: SliverGrid(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          mainAxisExtent: 72,
          crossAxisCount: 3,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
        ),
        delegate: SliverChildBuilderDelegate(
          (context, i) {
            Website website = websites[i];
            final bool isDarkMode =
                Provider.of<AppProvider>(context).isDarkMode(context);

            return Container(
              decoration: BoxDecoration(
                color: isDarkMode ? AppColors.boxDarkColor : Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isDarkMode
                      ? Colors.white.withOpacity(0.1)
                      : Colors.grey.shade200,
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: isDarkMode
                        ? Colors.black.withOpacity(0.2)
                        : Colors.grey.withOpacity(0.08),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                borderRadius: BorderRadius.circular(12),
                child: InkWell(
                  borderRadius: BorderRadius.circular(12),
                  onTap: () {
                    Navigator.of(context)
                        .push(MaterialPageRoute(builder: (context) {
                      return WebsiteLauncher(
                        url: website.url,
                        websiteName: website.name,
                      );
                    }));
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Center(
                      child: Image.network(
                        website.logo.toString(),
                        width: 80,
                        height: 80,
                        fit: BoxFit.contain,
                        errorBuilder: (context, error, stackTrace) {
                          return Icon(
                            Icons.language_rounded,
                            size: 32,
                            color: isDarkMode
                                ? Colors.white60
                                : Colors.grey.shade600,
                          );
                        },
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) return child;
                          return const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                AppColors.primaryColor,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
          childCount: websites.length,
        ),
      ),
    );
  }
}

class _SideHeader extends StatelessWidget {
  const _SideHeader({
    this.title,
  });

  final String? title;

  @override
  Widget build(BuildContext context) {
    final bool isDarkMode =
        Provider.of<AppProvider>(context).isDarkMode(context);

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 4.0),
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: isDarkMode ? AppColors.darkBodyColor : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDarkMode
              ? Colors.white.withOpacity(0.05)
              : Colors.grey.shade200,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 20,
            decoration: BoxDecoration(
              color: AppColors.primaryColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            '$title'.toUpperCase(),
            style: TextStyle(
              fontSize: 14,
              color: isDarkMode ? Colors.white : Colors.black87,
              fontWeight: FontWeight.w600,
              letterSpacing: 0.5,
              fontFamily: "NotoSans",
            ),
          ),
        ],
      ),
    );
  }
}

class WebsiteLoadingUI extends StatelessWidget {
  const WebsiteLoadingUI({super.key});

  @override
  Widget build(BuildContext context) {
    bool isDarkMode = Provider.of<AppProvider>(context).isDarkMode(context);
    return SliverStickyHeader(
      overlapsContent: false,
      header: Shimmer.fromColors(
        baseColor: isDarkMode ? AppColors.boxDarkColor : Colors.grey.shade300,
        highlightColor: isDarkMode ? Colors.black45 : Colors.grey.shade100,
        enabled: true,
        child: Container(
          height: 45,
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      sliver: SliverGrid(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          mainAxisExtent: 72,
          crossAxisCount: 3,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
        ),
        delegate: SliverChildBuilderDelegate(
          (context, i) => Shimmer.fromColors(
            baseColor:
                isDarkMode ? AppColors.boxDarkColor : Colors.grey.shade300,
            highlightColor: isDarkMode ? Colors.black45 : Colors.grey.shade100,
            enabled: true,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: isDarkMode
                        ? Colors.black.withOpacity(0.1)
                        : Colors.grey.withOpacity(0.05),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
            ),
          ),
          childCount: 18,
        ),
      ),
    );
  }
}
