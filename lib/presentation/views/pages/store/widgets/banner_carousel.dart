import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:goldenprizma/domain/store/models/home_data.dart';
import 'package:url_launcher/url_launcher.dart';

class BannerCarousel extends StatelessWidget {
  final Animation<double> fadeAnimation;
  final List<HomeBanner> banners;

  const BannerCarousel({
    super.key,
    required this.fadeAnimation,
    required this.banners,
  });

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
      child: AnimatedBuilder(
        animation: fadeAnimation,
        builder: (context, child) {
          return Transform.translate(
            offset: Offset(0, -20 * (1 - fadeAnimation.value)),
            child: Opacity(
              opacity: fadeAnimation.value,
              child: SizedBox(
                height: (96 + 20) *
                    fadeAnimation.value, // Reduced padding from 32 to 20
                child: fadeAnimation.value > 0.05
                    ? (banners.isEmpty
                        ? const SizedBox.shrink()
                        : Padding(
                            padding: const EdgeInsets.only(
                                top: 16.0,
                                right: 12.0,
                                left: 12.0,
                                bottom:
                                    4.0), // Reduced bottom padding from 16 to 4
                            child: SizedBox(
                              width: double.infinity,
                              height: 96, // Same height as home slider
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(
                                    8), // Same border radius as home
                                child: CarouselSlider.builder(
                                  itemCount: banners.length,
                                  itemBuilder: (context, index, realIndex) {
                                    final banner = banners[index];
                                    // Use localized image URL based on current locale
                                    final imageUrl =
                                        Directionality.of(context) ==
                                                TextDirection.rtl
                                            ? banner.imageArUrl
                                            : banner.imageEnUrl;

                                    return GestureDetector(
                                      onTap: () async {
                                        if (banner.actionUrl.isNotEmpty) {
                                          try {
                                            final uri =
                                                Uri.parse(banner.actionUrl);
                                            if (await canLaunchUrl(uri)) {
                                              await launchUrl(
                                                uri,
                                                mode: LaunchMode
                                                    .externalApplication,
                                              );
                                            }
                                          } catch (e) {
                                            // Handle URL launch error
                                            if (context.mounted) {
                                              ScaffoldMessenger.of(context)
                                                  .showSnackBar(
                                                const SnackBar(
                                                  content: Text(
                                                      'Could not open link'),
                                                ),
                                              );
                                            }
                                          }
                                        }
                                      },
                                      child: SizedBox(
                                        width: double.infinity,
                                        child: Image.network(
                                          imageUrl,
                                          fit: BoxFit.cover,
                                          width: double.infinity,
                                          errorBuilder:
                                              (context, error, stackTrace) =>
                                                  Container(
                                            color:
                                                Theme.of(context).dividerColor,
                                            child: const Icon(Icons.image,
                                                size: 40),
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                  options: CarouselOptions(
                                    height: 96, // Banner height
                                    autoPlay: true,
                                    aspectRatio: 416 /
                                        97, // Optimized for banner dimensions (416x97)
                                    viewportFraction: 1, // Full width display
                                    enableInfiniteScroll: true,
                                    autoPlayInterval:
                                        const Duration(seconds: 4),
                                  ),
                                ),
                              ),
                            ),
                          ))
                    : const SizedBox.shrink(),
              ),
            ),
          );
        },
      ),
    );
  }
}
