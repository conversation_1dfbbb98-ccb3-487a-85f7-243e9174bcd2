part of 'website_bloc.dart';

enum WebsiteStatus { initial, success, refresh, failure }

class WebsiteState extends Equatable {
  const WebsiteState({
    this.status = WebsiteStatus.initial,
    this.countryWebsites = const [],
    this.reachedMax = false,
    this.isFetching = false,
    this.failMessage = '',
    this.filters = const {},
  });

  final WebsiteStatus status;
  final List<CountryWebsites> countryWebsites;
  final bool reachedMax;
  final bool isFetching;
  final String failMessage;
  final Map<String, dynamic> filters;

  List<Website> get allWebsites => countryWebsites
      .map((country) => country.websites)
      .expand((websites) => websites)
      .toList();

  Website? findWebsiteById(int id) =>
      allWebsites.firstWhere((element) => element.id == id);

  Website? findWebsiteByUrl(String url) {
    Uri uri = Uri.parse(url);
    final matches =
        allWebsites.where((element) => element.url.contains(uri.host));

    if (matches.isEmpty) return null;

    // if one match is found then we will just return it
    if (matches.length == 1) return matches.first;

    // if multiple matches are found then we will check url path
    final path = uri.path;

    final pathMatches = matches.where((element) => element.url.contains(path));

    if (pathMatches.isEmpty) return matches.first;

    if (pathMatches.length == 1) return pathMatches.first;

    final hostMatches = matches.where((element) {
      try {
        final elUri = Uri.parse(element.url);
        return elUri.host == uri.host;
      } catch (e) {
        return false;
      }
    });

    return hostMatches.first;
  }

  @override
  List<Object> get props {
    return [
      status,
      countryWebsites,
      reachedMax,
      isFetching,
      failMessage,
      filters,
    ];
  }

  WebsiteState copyWith({
    WebsiteStatus? status,
    List<CountryWebsites>? countryWebsites,
    bool? reachedMax,
    bool? isFetching,
    String? failMessage,
    Map<String, dynamic>? filters,
  }) {
    return WebsiteState(
      status: status ?? this.status,
      countryWebsites: countryWebsites ?? this.countryWebsites,
      reachedMax: reachedMax ?? this.reachedMax,
      isFetching: isFetching ?? this.isFetching,
      failMessage: failMessage ?? this.failMessage,
      filters: filters ?? this.filters,
    );
  }

  @override
  String toString() {
    return 'WebsiteState(status: $status, countryWebsites: $countryWebsites, reachedMax: $reachedMax, isFetching: $isFetching, failMessage: $failMessage, filters: $filters)';
  }
}
