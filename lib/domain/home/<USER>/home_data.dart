import 'package:equatable/equatable.dart';
import 'package:goldenprizma/domain/exchange_rates/models/exchange_rate.dart';
import 'package:goldenprizma/domain/home/<USER>/slide.dart';

class HomeData extends Equatable {
  final List<ExchangeRate> exchangeRates;
  final List<Slide> slides;

  const HomeData({
    required this.exchangeRates,
    required this.slides,
  });

  @override
  List<Object> get props => [exchangeRates, slides];

  factory HomeData.fromJson(Map<String, dynamic> json) {
    final data = json['data'] ?? {};

    return HomeData(
      exchangeRates: (data['exchange_rates'] as List<dynamic>?)
              ?.map((item) => ExchangeRate.fromJson(item))
              .toList() ??
          [],
      slides: (data['slides'] as List<dynamic>?)
              ?.map((item) => Slide.fromJson(item))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'data': {
        'exchange_rates': exchangeRates.map((rate) => rate.toMap()).toList(),
        'slides': slides.map((slide) => slide.toJson()).toList(),
      }
    };
  }

  HomeData copyWith({
    List<ExchangeRate>? exchangeRates,
    List<Slide>? slides,
  }) {
    return HomeData(
      exchangeRates: exchangeRates ?? this.exchangeRates,
      slides: slides ?? this.slides,
    );
  }

  @override
  String toString() {
    return 'HomeData(exchangeRates: $exchangeRates, slides: $slides)';
  }
}
