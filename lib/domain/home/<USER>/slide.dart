import 'package:equatable/equatable.dart';

class Slide extends Equatable {
  final int id;
  final String? url;
  final String image;
  final String imageUrl;
  final String? isPage;
  final bool isActive;
  final bool isSlider;
  final int sortNumber;
  final String createdAt;

  const Slide({
    required this.id,
    this.url,
    required this.image,
    required this.imageUrl,
    this.isPage,
    required this.isActive,
    required this.isSlider,
    required this.sortNumber,
    required this.createdAt,
  });

  @override
  List<Object?> get props => [
        id,
        url,
        image,
        imageUrl,
        isPage,
        isActive,
        isSlider,
        sortNumber,
        createdAt,
      ];

  factory Slide.fromJson(Map<String, dynamic> json) {
    return Slide(
      id: json['id'] ?? 0,
      url: json['url'],
      image: json['image'] ?? '',
      imageUrl: json['image_url'] ?? '',
      isPage: json['is_page'],
      isActive: json['is_active'] ?? false,
      isSlider: json['is_slider'] ?? false,
      sortNumber: json['sort_number'] ?? 0,
      createdAt: json['created_at'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'url': url,
      'image': image,
      'image_url': imageUrl,
      'is_page': isPage,
      'is_active': isActive,
      'is_slider': isSlider,
      'sort_number': sortNumber,
      'created_at': createdAt,
    };
  }

  Slide copyWith({
    int? id,
    String? url,
    String? image,
    String? imageUrl,
    String? isPage,
    bool? isActive,
    bool? isSlider,
    int? sortNumber,
    String? createdAt,
  }) {
    return Slide(
      id: id ?? this.id,
      url: url ?? this.url,
      image: image ?? this.image,
      imageUrl: imageUrl ?? this.imageUrl,
      isPage: isPage ?? this.isPage,
      isActive: isActive ?? this.isActive,
      isSlider: isSlider ?? this.isSlider,
      sortNumber: sortNumber ?? this.sortNumber,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'Slide(id: $id, url: $url, image: $image, imageUrl: $imageUrl, isPage: $isPage, isActive: $isActive, isSlider: $isSlider, sortNumber: $sortNumber, createdAt: $createdAt)';
  }
}
