
import 'package:equatable/equatable.dart';

class Advertisement extends Equatable {
  final int id;
  final String url;
  final String image;
  final bool isPage;

  const Advertisement({
    required this.id,
    required this.url,
    required this.image,
    this.isPage = false,
  });

  Advertisement copyWith({
    int? id,
    String? url,
    String? image,
    bool? isPage,
  }) {
    return Advertisement(
      id: id ?? this.id,
      url: url ?? this.url,
      image: image ?? this.image,
      isPage: isPage ?? this.isPage,
    );
  }

  // Map<String, dynamic> toMap() {
  //   return {
  //     'id': id,
  //     'url': url,
  //     'image': image,
  //     'isSlide': isPage,
  //   };
  // }

  // factory Advertisement.fromMap(Map<String, dynamic> map) {
  //   return Advertisement(
  //     id: map['id']?.toInt() ?? 0,
  //     url: map['url'] ?? '',
  //     image: map['image'] ?? '',
  //     isPage: map['isSlide'] ?? false,
  //   );
  // }

  // String toJson() => json.encode(toMap());

  Advertisement.fromJson(Map<String, dynamic> jsonData)
      : id = jsonData['id'],
        url = jsonData['url'],
        image = jsonData['image'],
        isPage = jsonData['is_page'];

  @override
  String toString() {
    return 'Advertisement(id: $id, url: $url, image: $image, isSlide: $isPage)';
  }

  @override
  List<Object> get props => [id, url, image, isPage];
}
