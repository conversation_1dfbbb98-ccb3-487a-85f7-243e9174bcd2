name: goldenprizma
description: shipping and e-commerce app

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 2.0.2+37

environment:
  sdk: '>=3.1.2 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_bloc: ^8.1.3
  bloc: ^8.1.2
  cached_network_image: ^3.3.0
  carousel_slider: ^5.0.0
  cupertino_icons: ^1.0.6
  dio: ^5.3.3
  dotted_border: ^2.1.0
  dropdown_search: ^5.0.6
  easy_debounce: ^2.0.3
  equatable: ^2.0.5
  firebase_core: ^3.6.0
  firebase_messaging: ^15.2.5
  flutter_native_splash: ^2.4.1
  flutter_secure_storage: ^9.0.0
  flutter_svg: ^2.0.9
  flutter_verification_code: ^1.1.7
  formz: 0.4.1
  get_it: ^7.6.4
  image_picker: ^1.0.4
  image_cropper: ^4.0.1
  package_info_plus:
  intl: ^0.18.1
  intl_phone_number_input: ^0.7.3
  ionicons: ^0.2.2
  lottie: ^2.7.0
  modal_bottom_sheet: ^3.0.0-pre
  provider: ^6.1.1
  shared_preferences: ^2.2.2
  shimmer: ^3.0.0
  validators: ^3.0.0
  webview_flutter: ^4.9.0
  path_provider: ^2.1.1
  device_info_plus: ^9.1.0
  url_launcher: ^6.2.1
  flutter_local_notifications: ^17.2.3
  permission_handler: ^11.0.1
  new_version_plus:
  share_plus: ^7.2.1
  internet_connection_checker: ^1.0.0+1
  flutter_sticky_header: 0.6.5
  package_info_plus_web: ^2.0.0
  libphonenumber_plugin: ^0.3.3 
  hugeicons: ^0.0.7
  path: any
  image: any
  uuid: ^3.0.7
  firebase_crashlytics: ^4.3.5
  firebase_analytics: ^11.4.5
  smooth_page_indicator: ^1.2.1
  toastification: ^1.0.0

dependency_overrides:
    intl: ^0.18.1
    image: ^3.0.2
    package_info_plus: ^4.2.0
dev_dependencies:
  flutter_lints: ^3.0.1
  flutter_test:
    sdk: flutter
  flutter_launcher_icons: "^0.9.3"

flutter_native_splash:
  color: "#ffffff"
  image: assets/images/logo.png
  ios: true
  android: true
# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter.
flutter:
  generate: true
  uses-material-design: true
  assets:
    - assets/images/logo.png
    - assets/images/text_logo.png
    - assets/images/otp_icon.png
    - assets/images/sign_in_id.png
    - assets/images/flags/
    - assets/images/svg/
    - assets/images/png/
    - assets/images/logos/fib_logo.png
    - assets/success.json
    - assets/online_shop.json
    - assets/around_the_world.json
    - assets/wifi_connection.json
    - assets/empty_state.json
    - assets/processed_orders.json
    - assets/phone_verification_otp.json

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: NotoSans
      fonts:
        - asset: fonts/NotoSansArabic-Regular.ttf
        - asset: fonts/NotoSansArabic-Light.ttf
        - asset: fonts/NotoSansArabic-Medium.ttf
        - asset: fonts/NotoSansArabic-Bold.ttf
          weight: 400
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
